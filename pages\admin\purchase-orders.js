import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import PurchaseOrders from '@/components/admin/PurchaseOrders';

/**
 * Purchase Orders Management Page
 * 
 * This page provides a comprehensive interface for managing purchase orders,
 * including creation, tracking, and receiving workflows.
 */
export default function PurchaseOrdersPage() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!authLoading && user) {
      fetchPurchaseOrders();
    }
  }, [user, authLoading]);

  const fetchPurchaseOrders = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('/api/admin/purchase-orders', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('You do not have permission to access purchase orders.');
        }
        throw new Error(`Failed to fetch purchase orders: ${response.status}`);
      }

      const data = await response.json();
      setPurchaseOrders(data.purchaseOrders || []);
      
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while authenticating
  if (authLoading) {
    return (
      <>
        <Head>
          <title>Authenticating - Ocean Soul Sparkles Admin</title>
        </Head>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #667eea',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ color: '#64748b' }}>Authenticating...</p>
        </div>
      </>
    );
  }

  // Show error state if authentication failed
  if (!user) {
    return (
      <>
        <Head>
          <title>Authentication Required - Ocean Soul Sparkles Admin</title>
        </Head>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Authentication Required</h2>
          <p style={{ color: '#64748b' }}>Please log in to access the purchase orders page.</p>
        </div>
      </>
    );
  }

  // Check user permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <>
        <Head>
          <title>Access Denied - Ocean Soul Sparkles Admin</title>
        </Head>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Access Denied</h2>
          <p style={{ color: '#64748b' }}>You do not have permission to access purchase order management.</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Purchase Orders | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage purchase orders and supplier deliveries" />
      </Head>

      {error ? (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Error Loading Purchase Orders</h2>
          <p style={{ color: '#64748b' }}>{error}</p>
          <button 
            onClick={fetchPurchaseOrders}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      ) : (
        <PurchaseOrders 
          initialPurchaseOrders={purchaseOrders}
        />
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </>
  );
}
