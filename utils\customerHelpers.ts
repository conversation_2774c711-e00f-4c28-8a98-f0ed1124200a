/**
 * Customer Helper Functions
 * Shared utilities for customer display and management across admin modules
 */

// TypeScript interfaces for better type safety
export interface Customer {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  created_at: string;
  total_bookings?: number;
  is_vip?: boolean;
}

export interface PriorityInfo {
  label: string;
  color: string;
  icon: string;
}

export interface StatusInfo {
  status: string;
  label: string;
  className: string;
}

/**
 * Determine customer priority based on booking history and account age
 * @param {Object} customer - Customer object with total_bookings and created_at
 * @returns {string} Priority level: 'high', 'medium', or 'low'
 */
export const getCustomerPriority = (customer) => {
  const bookings = customer.total_bookings || 0;
  const createdDate = new Date(customer.created_at);
  const daysSinceCreated = (new Date() - createdDate) / (1000 * 60 * 60 * 24);

  // VIP customers or high booking count = High priority
  if (bookings >= 10 || customer.is_vip) {
    return 'high';
  }
  // Active customers with moderate bookings = Medium priority
  if (bookings >= 3 && daysSinceCreated > 30) {
    return 'medium';
  }
  // New customers or low activity = Low priority
  return 'low';
};

/**
 * Get priority display information including color, label, and icon
 * @param {string} priority - Priority level ('high', 'medium', 'low')
 * @returns {Object} Display info with label, color, and icon
 */
export const getPriorityInfo = (priority) => {
  switch (priority) {
    case 'high':
      return { label: 'High', color: '#dc3545', icon: '🔴' };
    case 'medium':
      return { label: 'Medium', color: '#ffc107', icon: '🟡' };
    case 'low':
    default:
      return { label: 'Low', color: '#28a745', icon: '🟢' };
  }
};

/**
 * Get customer status based on booking activity
 * @param {Object} customer - Customer object with total_bookings
 * @returns {string} Status: 'active' or 'new'
 */
export const getCustomerStatus = (customer) => {
  return (customer.total_bookings || 0) > 0 ? 'active' : 'new';
};

/**
 * Get customer status display information
 * @param {Customer} customer - Customer object with total_bookings
 * @returns {StatusInfo} Status info with label and CSS class
 */
export const getCustomerStatusInfo = (customer) => {
  const status = getCustomerStatus(customer);
  return {
    status,
    label: status === 'active' ? 'Active' : 'New',
    className: status === 'active' ? 'statusActive' : 'statusNew'
  };
};

/**
 * Format customer display name
 * @param {Object} customer - Customer object with first_name and last_name
 * @returns {string} Formatted full name
 */
export const getCustomerDisplayName = (customer) => {
  return `${customer.first_name || ''} ${customer.last_name || ''}`.trim();
};

/**
 * Get customer initials for avatar display
 * @param {Object} customer - Customer object with first_name and last_name
 * @returns {string} Customer initials (max 2 characters)
 */
export const getCustomerInitials = (customer) => {
  const firstName = customer.first_name || '';
  const lastName = customer.last_name || '';
  return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
};

/**
 * Format last booking date (placeholder implementation)
 * @param {Object} customer - Customer object
 * @returns {string} Formatted date or 'Never'
 */
export const getLastBookingDate = (customer) => {
  // For now, we'll use created_at as a placeholder since we don't have last booking data
  // In a real implementation, this would come from the API
  if (!customer.total_bookings || customer.total_bookings === 0) {
    return 'Never';
  }
  // Mock last booking date based on customer activity
  const mockLastBooking = new Date(customer.created_at);
  mockLastBooking.setDate(mockLastBooking.getDate() + (customer.total_bookings * 7));
  return mockLastBooking.toLocaleDateString('en-AU');
};

/**
 * Get customer ID display format
 * @param {string} customerId - Full customer ID
 * @returns {string} Formatted customer ID (last 6 characters, uppercase)
 */
export const getCustomerIdDisplay = (customerId) => {
  return `#${customerId.slice(-6).toUpperCase()}`;
};
