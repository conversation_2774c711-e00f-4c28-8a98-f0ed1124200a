/**
 * Test script to verify booking creation functionality
 * This script tests the fixed booking API endpoint
 */

const API_BASE_URL = 'http://localhost:3002/api';

// Test data from the database
const TEST_DATA = {
  customer_id: 'aaee60cd-8621-4c3e-af22-f27adf648e5b', // <EMAIL>
  service_id: '4bb610c5-9d80-4b4a-b86f-a942545b1f12', // Face Painting
  assigned_artist_id: '11111111-1111-1111-1111-111111111111', // <PERSON>
  tier_id: '1b01d3d4-b1b0-4c73-9635-cefd34addf7b', // Standard tier
  tier_name: 'Standard',
  tier_price: 40.00,
  start_time: '2024-12-20T10:00:00',
  end_time: '2024-12-20T10:10:00',
  status: 'confirmed',
  total_amount: 40.00,
  notes: 'Test booking created via API test script',
  location: 'Studio',
  booking_source: 'admin'
};

async function testBookingCreation() {
  console.log('🧪 Testing Booking Creation API...\n');

  try {
    // Step 1: Test authentication (you'll need to get a valid token)
    console.log('1. Testing booking creation...');
    
    const response = await fetch(`${API_BASE_URL}/admin/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, you'd need to authenticate and get a token
        'Authorization': 'Bearer YOUR_ADMIN_TOKEN_HERE'
      },
      body: JSON.stringify(TEST_DATA)
    });

    console.log(`   Response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Booking created successfully!');
      console.log(`   Booking ID: ${result.booking?.id}`);
      console.log(`   Customer: ${result.booking?.customer?.first_name || 'N/A'} ${result.booking?.customer?.last_name || 'N/A'}`);
      console.log(`   Service: ${result.booking?.service?.name}`);
      console.log(`   Artist: ${result.booking?.artist?.artist_name}`);
      console.log(`   Start Time: ${result.booking?.start_time}`);
      console.log(`   Total Amount: $${result.booking?.total_amount}`);
    } else {
      const error = await response.json();
      console.log('❌ Booking creation failed:');
      console.log(`   Error: ${error.error}`);
      console.log(`   Message: ${error.message || 'No additional message'}`);
      
      // Check if it's the service ID error we were fixing
      if (error.error === 'Invalid service ID') {
        console.log('🔍 This is the service ID validation error we were fixing!');
      }
    }

  } catch (error) {
    console.error('❌ Network error:', error.message);
  }

  console.log('\n📋 Test Data Used:');
  console.log(JSON.stringify(TEST_DATA, null, 2));
}

async function testServiceValidation() {
  console.log('\n🔍 Testing Service Validation...\n');

  try {
    // Test with valid service ID
    console.log('1. Testing with valid service ID...');
    const validResponse = await fetch(`${API_BASE_URL}/admin/services/4bb610c5-9d80-4b4a-b86f-a942545b1f12`, {
      headers: {
        'Authorization': 'Bearer YOUR_ADMIN_TOKEN_HERE'
      }
    });
    
    if (validResponse.ok) {
      const service = await validResponse.json();
      console.log(`✅ Valid service found: ${service.service?.name}`);
      console.log(`   Price: $${service.service?.price}`);
    } else {
      console.log('❌ Failed to fetch valid service');
    }

    // Test with invalid service ID
    console.log('\n2. Testing with invalid service ID...');
    const invalidResponse = await fetch(`${API_BASE_URL}/admin/services/invalid-service-id`, {
      headers: {
        'Authorization': 'Bearer YOUR_ADMIN_TOKEN_HERE'
      }
    });
    
    if (!invalidResponse.ok) {
      console.log('✅ Invalid service correctly rejected');
    } else {
      console.log('❌ Invalid service was accepted (unexpected)');
    }

  } catch (error) {
    console.error('❌ Service validation test error:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🚀 Ocean Soul Sparkles - Booking Creation Test\n');
  console.log('This script tests the fixed booking creation functionality.\n');
  
  await testServiceValidation();
  await testBookingCreation();
  
  console.log('\n📝 Notes:');
  console.log('- To run this test with authentication, you need to:');
  console.log('  1. Login to the admin panel');
  console.log('  2. Get the admin token from localStorage');
  console.log('  3. Replace "YOUR_ADMIN_TOKEN_HERE" with the actual token');
  console.log('- The main fix was changing "base_price" to "price" in the service validation query');
  console.log('- The booking API now properly validates service IDs against the services table');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testBookingCreation, testServiceValidation };
