import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import styles from '@/styles/admin/CustomerDetails.module.css';

export default function CustomerDetails() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [customer, setCustomer] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (id && !authLoading && user) {
      loadCustomer();
      loadCustomerBookings();
    }
  }, [id, authLoading, user]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customer details');
      }

      const data = await response.json();
      setCustomer(data.customer);
    } catch (error) {
      console.error('Error loading customer:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadCustomerBookings = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}/bookings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBookings(data.bookings || []);
      }
    } catch (error) {
      console.error('Error loading customer bookings:', error);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete customer');
      }

      router.push('/admin/customers');
    } catch (error) {
      console.error('Error deleting customer:', error);
      alert('Failed to delete customer: ' + error.message);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-AU');
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-AU');
  };

  const getBookingStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'cancelled': return '#dc3545';
      case 'completed': return '#17a2b8';
      default: return '#6c757d';
    }
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Customer Details - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading customer details...</p>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Head>
          <title>Error Loading Customer - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.errorContainer}>
          <h2>Error Loading Customer</h2>
          <p>{error}</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </>
    );
  }

  if (!customer) {
    return (
      <>
        <Head>
          <title>Customer Not Found - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.notFoundContainer}>
          <h2>Customer Not Found</h2>
          <p>The customer you're looking for doesn't exist.</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{customer.first_name} {customer.last_name} - Customer Details | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Details for customer ${customer.first_name} ${customer.last_name}`} />
      </Head>

      <div className={styles.customerDetailsContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/customers">Customers</Link>
            <span>/</span>
            <span>{customer.first_name} {customer.last_name}</span>
          </div>
          
          <div className={styles.headerActions}>
            <Link href={`/admin/customers/${customer.id}/edit`} className={styles.editButton}>
              ✏️ Edit Customer
            </Link>
            <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.bookButton}>
              📅 New Booking
            </Link>
            <button onClick={handleDelete} className={styles.deleteButton}>
              🗑️ Delete
            </button>
            <Link href="/admin/customers" className={styles.backButton}>
              ← Back to Customers
            </Link>
          </div>
        </header>

        {/* Primary Information - Always Visible */}
        <div className={styles.primaryInfo}>
          <div className={styles.customerHeader}>
            <div className={styles.customerAvatar}>
              {customer.first_name?.[0]}{customer.last_name?.[0]}
            </div>
            <div className={styles.customerDetails}>
              <h1 className={styles.customerName}>{customer.first_name} {customer.last_name}</h1>
              <p className={styles.customerEmail}>{customer.email}</p>
              <p className={styles.customerPhone}>{customer.phone || 'No phone number'}</p>
            </div>
            <div className={styles.customerStatus}>
              <span className={`${styles.statusBadge} ${
                (customer.total_bookings || 0) > 0 ? styles.statusActive : styles.statusNew
              }`}>
                {(customer.total_bookings || 0) > 0 ? 'Active Customer' : 'New Customer'}
              </span>
              <div className={styles.quickStats}>
                <div className={styles.statItem}>
                  <span className={styles.statValue}>{customer.total_bookings || 0}</span>
                  <span className={styles.statLabel}>Total Bookings</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statValue}>{formatDate(customer.created_at)}</span>
                  <span className={styles.statLabel}>Customer Since</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabbed Interface for Secondary/Tertiary Information */}
        <div className={styles.tabbedContent}>
          <div className={styles.tabNavigation}>
            <button
              onClick={() => setActiveTab('overview')}
              className={`${styles.tabButton} ${activeTab === 'overview' ? styles.active : ''}`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('bookings')}
              className={`${styles.tabButton} ${activeTab === 'bookings' ? styles.active : ''}`}
            >
              Booking History
            </button>
            <button
              onClick={() => setActiveTab('details')}
              className={`${styles.tabButton} ${activeTab === 'details' ? styles.active : ''}`}
            >
              Details & Notes
            </button>
          </div>

            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className={styles.overviewTab}>
                <div className={styles.quickActions}>
                  <h3>Quick Actions</h3>
                  <div className={styles.actionButtons}>
                    <Link href={`/admin/customers/${customer.id}/edit`} className={styles.actionButton}>
                      ✏️ Edit Customer
                    </Link>
                    <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.actionButton}>
                      📅 New Booking
                    </Link>
                  </div>
                </div>

                <div className={styles.recentActivity}>
                  <h3>Recent Bookings</h3>
                  {bookings.length === 0 ? (
                    <div className={styles.emptyState}>
                      <p>No bookings found</p>
                      <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.createBookingBtn}>
                        Create First Booking
                      </Link>
                    </div>
                  ) : (
                    <div className={styles.bookingsList}>
                      {bookings.slice(0, 3).map(booking => (
                        <div key={booking.id} className={styles.bookingItem}>
                          <div className={styles.bookingHeader}>
                            <span className={styles.serviceName}>{booking.service_name}</span>
                            <span
                              className={styles.bookingStatus}
                              style={{ backgroundColor: getBookingStatusColor(booking.status) }}
                            >
                              {booking.status}
                            </span>
                          </div>
                          <div className={styles.bookingDetails}>
                            <span>{formatDate(booking.booking_date)}</span>
                            <span>{booking.booking_time}</span>
                            <span>${booking.total_amount}</span>
                          </div>
                        </div>
                      ))}
                      {bookings.length > 3 && (
                        <button
                          onClick={() => setActiveTab('bookings')}
                          className={styles.viewAllBtn}
                        >
                          View All {bookings.length} Bookings →
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Booking History Tab */}
            {activeTab === 'bookings' && (
              <div className={styles.bookingsTab}>
                <h3>Complete Booking History</h3>
                {bookings.length === 0 ? (
                  <div className={styles.emptyState}>
                    <p>No bookings found for this customer</p>
                  </div>
                ) : (
                  <div className={styles.allBookingsList}>
                    {bookings.map(booking => (
                      <div key={booking.id} className={styles.bookingCard}>
                        <div className={styles.bookingHeader}>
                          <div className={styles.serviceInfo}>
                            <h4>{booking.service_name}</h4>
                            <p>{formatDate(booking.booking_date)} at {booking.booking_time}</p>
                          </div>
                          <span
                            className={styles.bookingStatus}
                            style={{ backgroundColor: getBookingStatusColor(booking.status) }}
                          >
                            {booking.status}
                          </span>
                        </div>
                        <div className={styles.bookingMeta}>
                          <span>Amount: ${booking.total_amount}</span>
                          <span>Duration: {booking.duration || 'N/A'} mins</span>
                          {booking.artist_name && <span>Artist: {booking.artist_name}</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Details & Notes Tab */}
            {activeTab === 'details' && (
              <div className={styles.detailsTab}>
                <div className={styles.detailsGrid}>
                  <div className={styles.detailCard}>
                    <h4>Contact Information</h4>
                    <div className={styles.contactInfo}>
                      <div className={styles.contactItem}>
                        <strong>Email:</strong> {customer.email || 'N/A'}
                      </div>
                      <div className={styles.contactItem}>
                        <strong>Phone:</strong> {customer.phone || 'N/A'}
                      </div>
                      {customer.phone_secondary && (
                        <div className={styles.contactItem}>
                          <strong>Secondary Phone:</strong> {customer.phone_secondary}
                        </div>
                      )}
                      <div className={styles.contactItem}>
                        <strong>Address:</strong> {customer.address || 'N/A'}
                      </div>
                    </div>
                  </div>

                  <div className={styles.detailCard}>
                    <h4>Personal Information</h4>
                    <div className={styles.personalInfo}>
                      <div className={styles.infoItem}>
                        <strong>Date of Birth:</strong> {formatDate(customer.date_of_birth)}
                      </div>
                      <div className={styles.infoItem}>
                        <strong>Customer Since:</strong> {formatDate(customer.created_at)}
                      </div>
                      <div className={styles.infoItem}>
                        <strong>Last Updated:</strong> {formatDateTime(customer.updated_at)}
                      </div>
                      <div className={styles.infoItem}>
                        <strong>Customer ID:</strong> {customer.id}
                      </div>
                    </div>
                  </div>

                  {customer.notes && (
                    <div className={styles.detailCard}>
                      <h4>Notes</h4>
                      <p className={styles.notes}>{customer.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
        </div>
      </div>
    </>
  );
}
