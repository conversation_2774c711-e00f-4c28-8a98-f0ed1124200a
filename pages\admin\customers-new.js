import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/Customers.module.css';

export default function CustomersManagement() {
  const { user, loading: authLoading } = useAuth();
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch('/api/admin/customers', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load customers');
      }

      const data = await response.json();
      setCustomers(data.customers || []);
      setFilteredCustomers(data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
      setCustomers([]);
      setFilteredCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadCustomers();
    }
  }, [user, authLoading]);

  useEffect(() => {
    let filtered = customers;
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    setFilteredCustomers(filtered);
  }, [customers, searchTerm]);

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Customers - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading customers...</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Customers Management - Ocean Soul Sparkles Admin</title>
      </Head>

      <div className={styles.customersContainer}>
        <header className={styles.header}>
          <h1>Customers Management</h1>
          <div className={styles.headerActions}>
            <button className={styles.newCustomerBtn}>
              + New Customer
            </button>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
        </div>

        <div className={styles.customersGrid}>
          {filteredCustomers.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No customers found.</p>
            </div>
          ) : (
            filteredCustomers.map(customer => (
              <div key={customer.id} className={styles.customerCard}>
                <div className={styles.customerHeader}>
                  <h3>{customer.name}</h3>
                  <span className={styles.bookingCount}>
                    {customer.total_bookings} bookings
                  </span>
                </div>
                <div className={styles.customerDetails}>
                  <p className={styles.customerEmail}>{customer.email}</p>
                  <p className={styles.customerPhone}>{customer.phone}</p>
                  {customer.notes && (
                    <p className={styles.customerNotes}>{customer.notes}</p>
                  )}
                </div>
                <div className={styles.customerActions}>
                  <button className={styles.viewBtn}>View Details</button>
                  <button className={styles.editBtn}>Edit</button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
}
