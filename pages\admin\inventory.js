import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { QuickExportButton } from '@/components/admin/ExportButton';
import styles from '@/styles/admin/Inventory.module.css';

/**
 * Inventory Management Page
 * 
 * This page provides a comprehensive interface for managing product inventory,
 * including stock levels, alerts, and product catalog.
 */
export default function InventoryManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [stockFilter, setStockFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Load inventory from API
  const loadInventory = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/inventory', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch inventory');
      }

      const data = await response.json();
      setInventory(data.inventory || []);
      setFilteredInventory(data.inventory || []);
    } catch (error) {
      console.error('Error loading inventory:', error);
      setInventory([]);
      setFilteredInventory([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadInventory();
    }
  }, [authLoading, user]);

  // Filter and sort inventory
  useEffect(() => {
    let filtered = inventory;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.sku && item.sku.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    // Filter by stock status
    if (stockFilter !== 'all') {
      filtered = filtered.filter(item => {
        if (stockFilter === 'in_stock') return item.status === 'in_stock';
        if (stockFilter === 'low_stock') return item.status === 'low_stock';
        if (stockFilter === 'out_of_stock') return item.status === 'out_of_stock';
        return true;
      });
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'stock':
          return (b.stock_quantity || 0) - (a.stock_quantity || 0);
        case 'price':
          return (b.sale_price || 0) - (a.sale_price || 0);
        default:
          return 0;
      }
    });

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, categoryFilter, stockFilter, sortBy]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'in_stock': return '#22c55e';
      case 'low_stock': return '#f59e0b';
      case 'out_of_stock': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Inventory - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading inventory...</p>
        </div>
      </>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  return (
    <>
      <Head>
        <title>Inventory Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage product inventory and stock levels" />
      </Head>

      <div className={styles.inventoryContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Inventory Management</h1>
          <div className={styles.headerActions}>
            <QuickExportButton
              data={filteredInventory}
              type="inventory"
              className={styles.exportBtn}
            />
            <Link href="/admin/inventory/new" className={styles.newItemBtn}>
              + Add Product
            </Link>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search products by name, category, or SKU..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filtersSection}>
            <div className={styles.filterGroup}>
              <label>Category:</label>
              <select 
                value={categoryFilter} 
                onChange={(e) => setCategoryFilter(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="all">All Categories</option>
                <option value="Braiding Hair">Braiding Hair</option>
                <option value="Marley Hair">Marley Hair</option>
                <option value="Hair Products">Hair Products</option>
                <option value="Accessories">Accessories</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label>Stock Status:</label>
              <select 
                value={stockFilter} 
                onChange={(e) => setStockFilter(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="all">All Stock Levels</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label>Sort by:</label>
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="name">Name</option>
                <option value="category">Category</option>
                <option value="stock">Stock Level</option>
                <option value="price">Price</option>
              </select>
            </div>
          </div>
        </div>

        <div className={styles.inventoryContent}>
          {filteredInventory.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No inventory items found</h3>
              <p>
                {inventory.length === 0 
                  ? "Get started by adding your first product to the inventory."
                  : "Try adjusting your search or filter criteria."
                }
              </p>
              <Link href="/admin/inventory/new" className={styles.addFirstBtn}>
                Add First Product
              </Link>
            </div>
          ) : (
            <div className={styles.inventoryGrid}>
              {filteredInventory.map((item) => (
                <div key={item.id} className={styles.inventoryCard}>
                  <div className={styles.cardHeader}>
                    <h3 className={styles.itemName}>{item.name}</h3>
                    <span 
                      className={styles.statusBadge}
                      style={{ backgroundColor: getStatusColor(item.status) }}
                    >
                      {item.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>

                  <div className={styles.cardBody}>
                    <div className={styles.itemInfo}>
                      <p className={styles.category}>{item.category}</p>
                      {item.sku && <p className={styles.sku}>SKU: {item.sku}</p>}
                      {item.description && <p className={styles.description}>{item.description}</p>}
                    </div>

                    <div className={styles.stockInfo}>
                      <div className={styles.stockLevel}>
                        <span className={styles.label}>Stock:</span>
                        <span className={styles.value}>
                          {item.stock_quantity || 0}
                          {item.min_stock_level && ` (Min: ${item.min_stock_level})`}
                        </span>
                      </div>
                      
                      {item.sale_price && (
                        <div className={styles.priceInfo}>
                          <span className={styles.label}>Price:</span>
                          <span className={styles.value}>{formatCurrency(item.sale_price)}</span>
                        </div>
                      )}
                      
                      {item.supplier && (
                        <div className={styles.supplierInfo}>
                          <span className={styles.label}>Supplier:</span>
                          <span className={styles.value}>{item.supplier}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className={styles.cardActions}>
                    <Link href={`/admin/inventory/${item.id}`} className={styles.viewBtn}>
                      View Details
                    </Link>
                    <button className={styles.editBtn}>
                      Edit
                    </button>
                    {(item.stock_quantity || 0) <= (item.min_stock_level || 0) && (
                      <button className={styles.restockBtn}>
                        Restock
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}