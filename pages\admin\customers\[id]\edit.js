import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/CustomerEdit.module.css';

export default function EditCustomer() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [customer, setCustomer] = useState(null);
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    phone_secondary: '',
    date_of_birth: '',
    address: '',
    notes: ''
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (id && !authLoading && user) {
      loadCustomer();
    }
  }, [id, authLoading, user]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customer details');
      }

      const data = await response.json();
      const customerData = data.customer;
      
      setCustomer(customerData);
      setFormData({
        first_name: customerData.first_name || '',
        last_name: customerData.last_name || '',
        email: customerData.email || '',
        phone: customerData.phone || '',
        phone_secondary: customerData.phone_secondary || '',
        date_of_birth: customerData.date_of_birth || '',
        address: customerData.address || '',
        notes: customerData.notes || ''
      });
    } catch (error) {
      console.error('Error loading customer:', error);
      toast.error('Failed to load customer details');
      setErrors({ load: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Customer updated successfully!');
        router.push(`/admin/customers/${id}`);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update customer');
      }
    } catch (error) {
      console.error('Error updating customer:', error);
      toast.error('Failed to update customer');
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading customer details...</p>
        </div>
      </>
    );
  }

  if (errors.load) {
    return (
      <>
        <Head>
          <title>Error - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.errorContainer}>
          <h1>Error Loading Customer</h1>
          <p>{errors.load}</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </>
    );
  }

  if (!customer) {
    return (
      <>
        <Head>
          <title>Customer Not Found - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.errorContainer}>
          <h1>Customer Not Found</h1>
          <p>The customer you're looking for doesn't exist.</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Edit {customer.first_name} {customer.last_name} | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Edit customer details for ${customer.first_name} ${customer.last_name}`} />
      </Head>

      <div className={styles.editCustomerContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/customers">Customers</Link>
            <span>/</span>
            <Link href={`/admin/customers/${customer.id}`}>{customer.first_name} {customer.last_name}</Link>
            <span>/</span>
            <span>Edit</span>
          </div>
          
          <div className={styles.headerActions}>
            <Link href={`/admin/customers/${customer.id}`} className={styles.cancelButton}>
              Cancel
            </Link>
          </div>
        </header>

        <div className={styles.formContainer}>
          <div className={styles.formHeader}>
            <h1>Edit Customer Details</h1>
            <p>Update information for {customer.first_name} {customer.last_name}</p>
          </div>

          <form onSubmit={handleSubmit} className={styles.customerForm}>
            <div className={styles.formGrid}>
              {/* Personal Information */}
              <div className={styles.formSection}>
                <h3>Personal Information</h3>
                
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="first_name">First Name *</label>
                    <input
                      type="text"
                      id="first_name"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleInputChange}
                      required
                      className={`${styles.formControl} ${errors.first_name ? styles.error : ''}`}
                    />
                    {errors.first_name && <span className={styles.errorText}>{errors.first_name}</span>}
                  </div>
                  
                  <div className={styles.formGroup}>
                    <label htmlFor="last_name">Last Name *</label>
                    <input
                      type="text"
                      id="last_name"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleInputChange}
                      required
                      className={`${styles.formControl} ${errors.last_name ? styles.error : ''}`}
                    />
                    {errors.last_name && <span className={styles.errorText}>{errors.last_name}</span>}
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="date_of_birth">Date of Birth</label>
                  <input
                    type="date"
                    id="date_of_birth"
                    name="date_of_birth"
                    value={formData.date_of_birth}
                    onChange={handleInputChange}
                    className={styles.formControl}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className={styles.formSection}>
                <h3>Contact Information</h3>
                
                <div className={styles.formGroup}>
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className={`${styles.formControl} ${errors.email ? styles.error : ''}`}
                  />
                  {errors.email && <span className={styles.errorText}>{errors.email}</span>}
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="phone">Primary Phone</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={styles.formControl}
                    />
                  </div>
                  
                  <div className={styles.formGroup}>
                    <label htmlFor="phone_secondary">Secondary Phone</label>
                    <input
                      type="tel"
                      id="phone_secondary"
                      name="phone_secondary"
                      value={formData.phone_secondary}
                      onChange={handleInputChange}
                      className={styles.formControl}
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="address">Address</label>
                  <textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    rows={3}
                    className={styles.formControl}
                    placeholder="Street address, city, state, postal code"
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className={styles.formSection}>
                <h3>Additional Information</h3>
                
                <div className={styles.formGroup}>
                  <label htmlFor="notes">Notes</label>
                  <textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={4}
                    className={styles.formControl}
                    placeholder="Any additional notes about this customer..."
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className={styles.formActions}>
              <Link href={`/admin/customers/${customer.id}`} className={styles.cancelButton}>
                Cancel
              </Link>
              <button
                type="submit"
                className={styles.submitButton}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
