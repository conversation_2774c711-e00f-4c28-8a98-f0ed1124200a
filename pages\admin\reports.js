import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import RevenueChart from '@/components/admin/charts/RevenueChart';
import BookingChart from '@/components/admin/charts/BookingChart';
import CustomerChart from '@/components/admin/charts/CustomerChart';
import DetailedBookingAnalytics from '@/components/admin/DetailedBookingAnalytics';
import styles from '@/styles/admin/Reports.module.css';

/**
 * Reports Management Page
 * 
 * This page provides comprehensive business analytics and reporting
 * including revenue, bookings, customer insights, and performance metrics.
 */
export default function ReportsManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('last30days');
  const [bookings, setBookings] = useState([]);
  const [reportData, setReportData] = useState({
    overview: {
      totalRevenue: 0,
      totalBookings: 0,
      totalCustomers: 0,
      averageBookingValue: 0,
      revenueGrowth: 0,
      bookingGrowth: 0
    },
    revenue: {
      daily: [],
      monthly: [],
      byService: [],
      byArtist: []
    },
    bookings: {
      statusBreakdown: [],
      servicePopularity: [],
      timeSlotAnalysis: [],
      cancellationRate: 0
    },
    customers: {
      newCustomers: 0,
      returningCustomers: 0,
      customerLifetimeValue: 0,
      topCustomers: []
    }
  });

  useEffect(() => {
    if (user) {
      loadReportData();
    }
  }, [user, dateRange]);

  const loadReportData = async () => {
    try {
      setLoading(true);

      // Load bookings data for detailed analytics
      const bookingsResponse = await fetch('/api/admin/bookings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (bookingsResponse.ok) {
        const bookingsData = await bookingsResponse.json();
        setBookings(bookingsData.bookings || []);
      }

      const response = await fetch(`/api/admin/reports?range=${dateRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReportData(data.reports || reportData);
      } else {
        console.log('Using mock data - API not available');
        // Mock data for development
        setReportData({
          overview: {
            totalRevenue: 15420.50,
            totalBookings: 127,
            totalCustomers: 89,
            averageBookingValue: 121.42,
            revenueGrowth: 12.5,
            bookingGrowth: 8.3
          },
          revenue: {
            daily: [
              { date: '2024-01-01', amount: 450 },
              { date: '2024-01-02', amount: 320 },
              { date: '2024-01-03', amount: 680 }
            ],
            byService: [
              { service: 'Hair Braiding', amount: 8500, percentage: 55 },
              { service: 'Hair Styling', amount: 4200, percentage: 27 },
              { service: 'Hair Extensions', amount: 2720, percentage: 18 }
            ]
          },
          bookings: {
            statusBreakdown: [
              { status: 'Completed', count: 95, percentage: 75 },
              { status: 'Confirmed', count: 20, percentage: 16 },
              { status: 'Cancelled', count: 12, percentage: 9 }
            ],
            cancellationRate: 9.4
          },
          customers: {
            newCustomers: 34,
            returningCustomers: 55,
            customerLifetimeValue: 173.25
          }
        });
      }
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format) => {
    try {
      const response = await fetch(`/api/admin/reports/export?format=${format}&range=${dateRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ocean-soul-sparkles-report-${dateRange}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Export feature not available yet');
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      alert('Export feature not available yet');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Reports - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading reports...</p>
        </div>
      </>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  // Check permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <>
        <Head>
          <title>Access Denied - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.accessDenied}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access reports.</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Reports & Analytics | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Business analytics and reporting dashboard" />
      </Head>

      <div className={styles.reportsContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Reports & Analytics</h1>
          <div className={styles.headerActions}>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className={styles.dateRangeSelect}
            >
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="last90days">Last 90 Days</option>
              <option value="thisyear">This Year</option>
              <option value="custom">Custom Range</option>
            </select>
            <button 
              onClick={() => exportReport('pdf')}
              className={styles.exportBtn}
            >
              Export PDF
            </button>
            <button 
              onClick={() => exportReport('csv')}
              className={styles.exportBtn}
            >
              Export CSV
            </button>
          </div>
        </header>

        <div className={styles.reportsContent}>
          <nav className={styles.tabNavigation}>
            <button
              className={`${styles.tabButton} ${activeTab === 'overview' ? styles.active : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'revenue' ? styles.active : ''}`}
              onClick={() => setActiveTab('revenue')}
            >
              Revenue
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'bookings' ? styles.active : ''}`}
              onClick={() => setActiveTab('bookings')}
            >
              Bookings
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'booking-analytics' ? styles.active : ''}`}
              onClick={() => setActiveTab('booking-analytics')}
            >
              Booking Analytics
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'customers' ? styles.active : ''}`}
              onClick={() => setActiveTab('customers')}
            >
              Customers
            </button>
          </nav>

          <div className={styles.tabContent}>
            {activeTab === 'overview' && (
              <div className={styles.overviewSection}>
                <div className={styles.metricsGrid}>
                  <div className={styles.metricCard}>
                    <h3>Total Revenue</h3>
                    <div className={styles.metricValue}>
                      {formatCurrency(reportData.overview.totalRevenue)}
                    </div>
                    <div className={styles.metricChange}>
                      {formatPercentage(reportData.overview.revenueGrowth)} vs previous period
                    </div>
                  </div>
                  <div className={styles.metricCard}>
                    <h3>Total Bookings</h3>
                    <div className={styles.metricValue}>
                      {reportData.overview.totalBookings}
                    </div>
                    <div className={styles.metricChange}>
                      {formatPercentage(reportData.overview.bookingGrowth)} vs previous period
                    </div>
                  </div>
                  <div className={styles.metricCard}>
                    <h3>Total Customers</h3>
                    <div className={styles.metricValue}>
                      {reportData.overview.totalCustomers}
                    </div>
                  </div>
                  <div className={styles.metricCard}>
                    <h3>Average Booking Value</h3>
                    <div className={styles.metricValue}>
                      {formatCurrency(reportData.overview.averageBookingValue)}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'revenue' && (
              <div className={styles.revenueSection}>
                <h2>Revenue Analysis</h2>
                <RevenueChart
                  data={reportData.revenue}
                  dateRange={dateRange.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, str => str.toUpperCase())}
                />
              </div>
            )}

            {activeTab === 'bookings' && (
              <div className={styles.bookingsSection}>
                <h2>Booking Analysis</h2>
                <BookingChart
                  data={reportData.bookings}
                  dateRange={dateRange.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, str => str.toUpperCase())}
                />
              </div>
            )}

            {activeTab === 'booking-analytics' && (
              <div className={styles.bookingAnalyticsSection}>
                <h2>Comprehensive Booking Analytics</h2>
                <p className={styles.sectionDescription}>
                  Detailed booking analytics including status breakdowns, top services, artist performance, and trends.
                </p>
                <DetailedBookingAnalytics
                  bookings={bookings}
                  dateRange={dateRange.replace('last', '').replace('days', 'd')}
                />
              </div>
            )}

            {activeTab === 'customers' && (
              <div className={styles.customersSection}>
                <h2>Customer Analysis</h2>
                <CustomerChart
                  data={reportData.customers}
                  dateRange={dateRange.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, str => str.toUpperCase())}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
