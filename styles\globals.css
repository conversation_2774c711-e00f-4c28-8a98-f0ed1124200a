/* Ocean Soul Sparkles Admin Portal - Global Styles */

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
}

/* Admin Portal Color Scheme */
:root {
  /* Primary Colors (Teal/Sea Blue Focus) */
  --admin-primary: #007A7A; /* Deep Teal */
  --admin-primary-dark: #005050; /* Darker Teal */
  --admin-primary-light: #00A0A0; /* Lighter <PERSON>l */
  
  /* Existing Accent (Retained) */
  --admin-accent: #4ECDC4; /* Turquoise */

  /* New Accent Colors */
  --admin-accent-warm: #FF7F50; /* Coral */
  --admin-accent-sparkle: #E0FFFF; /* Light Cyan "Sparkle" */
  
  /* Status Colors (Standard - can be tweaked later if needed) */
  --admin-success: #28a745;
  --admin-warning: #ffc107;
  --admin-danger: #dc3545;
  --admin-info: #0d6efd; /* Changed from #17a2b8 to standard Bootstrap info blue */
  /* Consider changing --admin-info to something like #007bff (standard Bootstrap info blue) to differentiate from primary teal */

  /* Neutral Colors (Text, Backgrounds, Borders) */
  --admin-white: #ffffff;
  --admin-light: #F0F4F8; /* Page content BG - Was #f8f9fa */
  --admin-lighter: #E1E8ED; /* Subtle dividers, hover BGs - Was #e9ecef */

  --admin-gray: #5D6D7E; /* Secondary Text, Borders - Was #6c757d */
  --admin-dark: #4A5B6C; /* Darker Gray - Refined from original --admin-dark for thematic consistency */
  --admin-darker: #2A3B4C; /* Main Text Color - Was #2c3e50 */
  
  /* Background Colors */
  --admin-bg-primary: var(--admin-white); /* Card/Header/Sidebar BG - Remains #ffffff */
  --admin-bg-secondary: var(--admin-light); /* Page Content BG - Now #F0F4F8 */
  --admin-bg-tertiary: var(--admin-lighter); /* Hover BGs, subtle dividers - Now #E1E8ED */
  
  /* Border Colors */
  --admin-border-light: var(--admin-lighter); /* Now #E1E8ED */
  --admin-border-medium: #C0CADC; /* Cooler mid-tone border - Was #dee2e6 */
  --admin-border-dark: var(--admin-gray); /* Now #5D6D7E - Was #adb5bd */
  
  /* Shadow Colors (Can remain as they are general purpose) */
  --admin-shadow-light: rgba(0, 0, 0, 0.08); /* Slightly softer shadow */
  --admin-shadow-medium: rgba(0, 0, 0, 0.12); /* Slightly softer shadow */
  --admin-shadow-dark: rgba(0, 0, 0, 0.20); /* Slightly softer shadow */

  /* --- Other variables (spacing, radius, transitions, z-index) remain unchanged --- */
  --admin-primary-rgb: 0, 122, 122;
  --admin-primary-light-transparent: rgba(var(--admin-primary-rgb), 0.25);
  --admin-danger-rgb: 220, 53, 69;
  --admin-danger-light-transparent: rgba(var(--admin-danger-rgb), 0.2);
  --admin-danger-light-bg: rgba(var(--admin-danger-rgb), 0.1);
  --admin-success-rgb: 40, 167, 69;
  --admin-success-light-bg: rgba(var(--admin-success-rgb), 0.1);
  --admin-warning-rgb: 255, 193, 7;
  --admin-warning-light-bg: rgba(var(--admin-warning-rgb), 0.15);
  --admin-info-rgb: 13, 110, 253;
  --admin-info-light-bg: rgba(var(--admin-info-rgb), 0.1);
  /* Spacing */
  --admin-spacing-xs: 4px;
  --admin-spacing-sm: 8px;
  --admin-spacing-md: 16px;
  --admin-spacing-lg: 24px;
  --admin-spacing-xl: 32px;
  --admin-spacing-xxl: 48px;
  
  /* Border Radius */
  --admin-radius-sm: 4px;
  --admin-radius-md: 8px;
  --admin-radius-lg: 12px;
  --admin-radius-xl: 16px;
  
  /* Transitions */
  --admin-transition-fast: 0.15s ease;
  --admin-transition-normal: 0.2s ease;
  --admin-transition-slow: 0.3s ease;
  
  /* Z-Index Scale */
  --admin-z-dropdown: 1000;
  --admin-z-sticky: 1020;
  --admin-z-fixed: 1030;
  --admin-z-modal-backdrop: 1040;
  --admin-z-modal: 1050;
  --admin-z-popover: 1060;
  --admin-z-tooltip: 1070;
  --admin-z-toast: 1080;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  color: var(--admin-darker);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

a {
  color: var(--admin-primary);
  text-decoration: none;
  transition: color var(--admin-transition-normal);
}

a:hover {
  color: var(--admin-primary-dark);
  text-decoration: underline;
}

/* Form Elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Enhanced Form Element Styling */
label {
  display: block;
  margin-bottom: var(--admin-spacing-sm);
  color: var(--admin-darker);
  font-weight: 500;
  font-size: 0.9rem;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
  display: block;
  width: 100%;
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--admin-darker);
  background-color: var(--admin-white);
  background-clip: padding-box;
  border: 1px solid var(--admin-border-medium);
  border-radius: var(--admin-radius-md);
  transition: border-color var(--admin-transition-fast) ease-in-out, box-shadow var(--admin-transition-fast) ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-light-transparent);
}

/* Placeholder text color */
input::placeholder,
textarea::placeholder {
  color: var(--admin-gray);
  opacity: 0.8;
}

/* Disabled state */
input:disabled,
textarea:disabled,
select:disabled {
  background-color: var(--admin-bg-tertiary);
  opacity: 0.7;
  cursor: not-allowed;
}

/* Readonly state */
input[readonly],
textarea[readonly] {
  background-color: var(--admin-bg-tertiary);
}

/* Input with error */
.input-error {
  border-color: var(--admin-danger) !important; /* Important to override focus potentially */
}
.input-error:focus {
  border-color: var(--admin-danger) !important;
  box-shadow: 0 0 0 3px var(--admin-danger-light-transparent) !important;
}
.error-text {
  display: block;
  font-size: 0.8rem;
  color: var(--admin-danger);
  margin-top: var(--admin-spacing-xs);
}

/* Checkbox and Radio */
input[type="checkbox"],
input[type="radio"] {
  accent-color: var(--admin-primary);
  margin-right: var(--admin-spacing-sm);
  width: 1em;
  height: 1em;
}

/* File Input - basic styling, can be enhanced with custom components */
input[type="file"] {
  border: 1px solid var(--admin-border-medium);
  padding: var(--admin-spacing-sm);
  border-radius: var(--admin-radius-md);
}
input[type="file"]::file-selector-button {
  margin-right: var(--admin-spacing-md);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border: none;
  border-radius: var(--admin-radius-sm);
  background-color: var(--admin-primary);
  color: var(--admin-white);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--admin-transition-fast);
}
input[type="file"]::file-selector-button:hover {
  background-color: var(--admin-primary-dark);
}

/* Form Grouping (optional utility) */
.form-group {
  margin-bottom: var(--admin-spacing-lg);
}

/* Alert Boxes */
.alert {
  padding: var(--admin-spacing-md);
  margin-bottom: var(--admin-spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--admin-radius-md);
  font-size: 0.9rem;
}
.alert-danger {
  color: var(--admin-danger);
  background-color: var(--admin-danger-light-bg);
  border-color: var(--admin-danger-light-transparent); /* Use a slightly more opaque border */
}
.alert-success {
  color: var(--admin-success);
  background-color: var(--admin-success-light-bg);
  border-color: rgba(var(--admin-success-rgb), 0.2);
}
.alert-warning {
  color: #664d03; /* Darker yellow for text on light yellow bg */
  background-color: var(--admin-warning-light-bg);
  border-color: rgba(var(--admin-warning-rgb), 0.25);
}
.alert-info {
  color: var(--admin-info);
  background-color: var(--admin-info-light-bg);
  border-color: rgba(var(--admin-info-rgb), 0.2);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--admin-primary); }
.text-secondary { color: var(--admin-secondary); }
.text-success { color: var(--admin-success); }
.text-warning { color: var(--admin-warning); }
.text-danger { color: var(--admin-danger); }
.text-info { color: var(--admin-info); }

.bg-primary { background-color: var(--admin-primary); }
.bg-secondary { background-color: var(--admin-bg-secondary); }
.bg-white { background-color: var(--admin-white); }

.border { border: 1px solid var(--admin-border-light); }
.border-top { border-top: 1px solid var(--admin-border-light); }
.border-bottom { border-bottom: 1px solid var(--admin-border-light); }

.rounded { border-radius: var(--admin-radius-md); }
.rounded-lg { border-radius: var(--admin-radius-lg); }
.rounded-xl { border-radius: var(--admin-radius-xl); }

.shadow { box-shadow: 0 2px 4px var(--admin-shadow-light); }
.shadow-md { box-shadow: 0 4px 8px var(--admin-shadow-light); }
.shadow-lg { box-shadow: 0 8px 16px var(--admin-shadow-medium); }

.transition { transition: all var(--admin-transition-normal); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }

/* Grid Utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-1 { gap: var(--admin-spacing-xs); }
.gap-2 { gap: var(--admin-spacing-sm); }
.gap-4 { gap: var(--admin-spacing-md); }
.gap-6 { gap: var(--admin-spacing-lg); }
.gap-8 { gap: var(--admin-spacing-xl); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--admin-spacing-xs); }
.m-2 { margin: var(--admin-spacing-sm); }
.m-4 { margin: var(--admin-spacing-md); }
.m-6 { margin: var(--admin-spacing-lg); }
.m-8 { margin: var(--admin-spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--admin-spacing-xs); }
.p-2 { padding: var(--admin-spacing-sm); }
.p-4 { padding: var(--admin-spacing-md); }
.p-6 { padding: var(--admin-spacing-lg); }
.p-8 { padding: var(--admin-spacing-xl); }

/* Width and Height Utilities */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow Utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Admin-specific Components */
.admin-card {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
  transition: box-shadow var(--admin-transition-normal);
}

.admin-card:hover {
  box-shadow: 0 4px 8px var(--admin-shadow-medium);
}

.admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  background: var(--admin-primary);
  color: var(--admin-white);
  border: none;
  border-radius: var(--admin-radius-md);
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  text-decoration: none;
}

.admin-button:hover:not(:disabled) {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 136, 216, 0.3);
}

.admin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-button.secondary {
  background: var(--admin-secondary);
  color: var(--admin-white);
}

.admin-button.secondary:hover:not(:disabled) {
  background: var(--admin-dark);
}

.admin-button.outline {
  background: transparent;
  color: var(--admin-primary);
  border: 2px solid var(--admin-primary);
}

.admin-button.outline:hover:not(:disabled) {
  background: var(--admin-primary);
  color: var(--admin-white);
}

/* Loading Spinner */
.admin-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
  border-radius: var(--admin-radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.success {
  background: rgba(40, 167, 69, 0.1);
  color: var(--admin-success);
}

.status-indicator.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.status-indicator.danger {
  background: rgba(220, 53, 69, 0.1);
  color: var(--admin-danger);
}

.status-indicator.info {
  background: rgba(23, 162, 184, 0.1);
  color: var(--admin-info);
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --admin-spacing-xs: 2px;
    --admin-spacing-sm: 4px;
    --admin-spacing-md: 8px;
    --admin-spacing-lg: 16px;
    --admin-spacing-xl: 24px;
    --admin-spacing-xxl: 32px;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.1rem; }
  h6 { font-size: 1rem; }

  .admin-card {
    padding: var(--admin-spacing-md);
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .admin-button,
  .admin-spinner,
  .status-indicator {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --admin-border-light: #000000;
    --admin-border-medium: #000000;
    --admin-shadow-light: rgba(0, 0, 0, 0.5);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
