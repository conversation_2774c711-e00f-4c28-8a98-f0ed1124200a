import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import styles from '@/styles/admin/Settings.module.css';

/**
 * Settings Management Page
 * 
 * This page provides a comprehensive interface for managing system settings,
 * business configuration, and admin preferences.
 */
export default function SettingsManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      businessName: 'Ocean Soul Sparkles',
      businessEmail: '<EMAIL>',
      businessPhone: '+61 XXX XXX XXX',
      businessAddress: 'Australia',
      timezone: 'Australia/Sydney',
      currency: 'AUD'
    },
    booking: {
      defaultBookingDuration: 60,
      advanceBookingDays: 30,
      cancellationHours: 24,
      autoConfirmBookings: true,
      requireDeposit: false,
      depositPercentage: 20
    },
    payment: {
      squareEnabled: true,
      squareEnvironment: 'sandbox',
      cashEnabled: true,
      cardEnabled: true,
      allowPartialPayments: true,
      autoProcessRefunds: false
    },
    notifications: {
      // Global toggles
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: false,

      // Email notification types
      emailBookingConfirmation: true,
      emailBookingReminder: true,
      emailBookingCancellation: true,
      emailPaymentReceipt: true,
      emailStaffNotification: true,
      emailLowInventoryAlert: true,
      emailPromotional: true,

      // SMS notification types
      smsBookingConfirmation: false,
      smsBookingReminder: false,
      smsBookingCancellation: false,
      smsPaymentReceipt: false,
      smsStaffNotification: false,
      smsPromotional: false,

      // Push notification types (for future)
      pushBookingConfirmation: false,
      pushBookingReminder: false,
      pushBookingCancellation: false,
      pushStaffNotification: false,

      // General settings
      bookingReminders: true,
      reminderHours: 24,
      adminNotifications: true,
      customerNotifications: true,

      // Fallback behavior
      emailFallbackWhenSMSFails: true,
      smsFallbackWhenEmailFails: false
    },
    security: {
      sessionTimeout: 1800,
      adminSessionTimeout: 1800,
      maxLoginAttempts: 5,
      lockoutDuration: 900,
      requireMFA: false,
      ipRestrictions: false
    }
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      loadSettings();
    }
  }, [user]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data.settings || settings);
      } else {
        console.log('Using default settings - API not available');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({ settings })
      });

      if (response.ok) {
        setHasChanges(false);
        alert('Settings saved successfully!');
      } else {
        alert('Failed to save settings. Please try again.');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      loadSettings();
      setHasChanges(false);
    }
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Settings - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading settings...</p>
        </div>
      </>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  // Check permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <>
        <Head>
          <title>Access Denied - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.accessDenied}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access system settings.</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Settings | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage system settings and configuration" />
      </Head>

      <div className={styles.settingsContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Settings</h1>
          <div className={styles.headerActions}>
            {hasChanges && (
              <>
                <button 
                  onClick={resetSettings}
                  className={styles.resetBtn}
                  disabled={saving}
                >
                  Reset
                </button>
                <button 
                  onClick={saveSettings}
                  className={styles.saveBtn}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </>
            )}
          </div>
        </header>

        <div className={styles.settingsContent}>
          <nav className={styles.tabNavigation}>
            <button
              className={`${styles.tabButton} ${activeTab === 'general' ? styles.active : ''}`}
              onClick={() => setActiveTab('general')}
            >
              General
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'booking' ? styles.active : ''}`}
              onClick={() => setActiveTab('booking')}
            >
              Booking
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'payment' ? styles.active : ''}`}
              onClick={() => setActiveTab('payment')}
            >
              Payment
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'notifications' ? styles.active : ''}`}
              onClick={() => setActiveTab('notifications')}
            >
              Notifications
            </button>
            <button
              className={`${styles.tabButton} ${activeTab === 'security' ? styles.active : ''}`}
              onClick={() => setActiveTab('security')}
            >
              Security
            </button>
          </nav>

          <div className={styles.tabContent}>
            {activeTab === 'general' && (
              <div className={styles.settingsSection}>
                <h2>General Settings</h2>
                <div className={styles.settingsGrid}>
                  <div className={styles.settingItem}>
                    <label>Business Name</label>
                    <input
                      type="text"
                      value={settings.general.businessName}
                      onChange={(e) => updateSetting('general', 'businessName', e.target.value)}
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Business Email</label>
                    <input
                      type="email"
                      value={settings.general.businessEmail}
                      onChange={(e) => updateSetting('general', 'businessEmail', e.target.value)}
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Business Phone</label>
                    <input
                      type="tel"
                      value={settings.general.businessPhone}
                      onChange={(e) => updateSetting('general', 'businessPhone', e.target.value)}
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Business Address</label>
                    <textarea
                      value={settings.general.businessAddress}
                      onChange={(e) => updateSetting('general', 'businessAddress', e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Timezone</label>
                    <select
                      value={settings.general.timezone}
                      onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
                    >
                      <option value="Australia/Sydney">Australia/Sydney</option>
                      <option value="Australia/Melbourne">Australia/Melbourne</option>
                      <option value="Australia/Brisbane">Australia/Brisbane</option>
                      <option value="Australia/Perth">Australia/Perth</option>
                    </select>
                  </div>
                  <div className={styles.settingItem}>
                    <label>Currency</label>
                    <select
                      value={settings.general.currency}
                      onChange={(e) => updateSetting('general', 'currency', e.target.value)}
                    >
                      <option value="AUD">AUD - Australian Dollar</option>
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'booking' && (
              <div className={styles.settingsSection}>
                <h2>Booking Settings</h2>
                <div className={styles.settingsGrid}>
                  <div className={styles.settingItem}>
                    <label>Default Booking Duration (minutes)</label>
                    <input
                      type="number"
                      value={settings.booking.defaultBookingDuration}
                      onChange={(e) => updateSetting('booking', 'defaultBookingDuration', parseInt(e.target.value))}
                      min="15"
                      max="480"
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Advance Booking Days</label>
                    <input
                      type="number"
                      value={settings.booking.advanceBookingDays}
                      onChange={(e) => updateSetting('booking', 'advanceBookingDays', parseInt(e.target.value))}
                      min="1"
                      max="365"
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>Cancellation Notice (hours)</label>
                    <input
                      type="number"
                      value={settings.booking.cancellationHours}
                      onChange={(e) => updateSetting('booking', 'cancellationHours', parseInt(e.target.value))}
                      min="1"
                      max="168"
                    />
                  </div>
                  <div className={styles.settingItem}>
                    <label>
                      <input
                        type="checkbox"
                        checked={settings.booking.autoConfirmBookings}
                        onChange={(e) => updateSetting('booking', 'autoConfirmBookings', e.target.checked)}
                      />
                      Auto-confirm bookings
                    </label>
                  </div>
                  <div className={styles.settingItem}>
                    <label>
                      <input
                        type="checkbox"
                        checked={settings.booking.requireDeposit}
                        onChange={(e) => updateSetting('booking', 'requireDeposit', e.target.checked)}
                      />
                      Require deposit
                    </label>
                  </div>
                  {settings.booking.requireDeposit && (
                    <div className={styles.settingItem}>
                      <label>Deposit Percentage</label>
                      <input
                        type="number"
                        value={settings.booking.depositPercentage}
                        onChange={(e) => updateSetting('booking', 'depositPercentage', parseInt(e.target.value))}
                        min="5"
                        max="100"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional tabs would be implemented similarly */}
            {activeTab === 'payment' && (
              <div className={styles.settingsSection}>
                <h2>Payment Settings</h2>
                <p className={styles.comingSoon}>Payment settings configuration coming soon...</p>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className={styles.settingsSection}>
                <h2>Notification Settings</h2>

                {/* Global Communication Toggles */}
                <div className={styles.settingsGroup}>
                  <h3>Communication Channels</h3>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailNotifications}
                          onChange={(e) => updateSetting('notifications', 'emailNotifications', e.target.checked)}
                        />
                        Enable Email Notifications
                      </label>
                      <small>Master toggle for all email communications</small>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsNotifications}
                          onChange={(e) => updateSetting('notifications', 'smsNotifications', e.target.checked)}
                        />
                        Enable SMS Notifications
                      </label>
                      <small>Master toggle for all SMS communications</small>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.pushNotifications}
                          onChange={(e) => updateSetting('notifications', 'pushNotifications', e.target.checked)}
                        />
                        Enable Push Notifications
                      </label>
                      <small>Master toggle for push notifications (future feature)</small>
                    </div>
                  </div>
                </div>

                {/* Email Notification Types */}
                <div className={styles.settingsGroup}>
                  <h3>Email Notifications</h3>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailBookingConfirmation}
                          onChange={(e) => updateSetting('notifications', 'emailBookingConfirmation', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Booking Confirmations
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailBookingReminder}
                          onChange={(e) => updateSetting('notifications', 'emailBookingReminder', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Booking Reminders
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailBookingCancellation}
                          onChange={(e) => updateSetting('notifications', 'emailBookingCancellation', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Booking Cancellations
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailPaymentReceipt}
                          onChange={(e) => updateSetting('notifications', 'emailPaymentReceipt', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Payment Receipts
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailStaffNotification}
                          onChange={(e) => updateSetting('notifications', 'emailStaffNotification', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Staff Notifications
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailPromotional}
                          onChange={(e) => updateSetting('notifications', 'emailPromotional', e.target.checked)}
                          disabled={!settings.notifications.emailNotifications}
                        />
                        Promotional Emails
                      </label>
                    </div>
                  </div>
                </div>

                {/* SMS Notification Types */}
                <div className={styles.settingsGroup}>
                  <h3>SMS Notifications</h3>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsBookingConfirmation}
                          onChange={(e) => updateSetting('notifications', 'smsBookingConfirmation', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Booking Confirmations
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsBookingReminder}
                          onChange={(e) => updateSetting('notifications', 'smsBookingReminder', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Booking Reminders
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsBookingCancellation}
                          onChange={(e) => updateSetting('notifications', 'smsBookingCancellation', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Booking Cancellations
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsPaymentReceipt}
                          onChange={(e) => updateSetting('notifications', 'smsPaymentReceipt', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Payment Receipts
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsStaffNotification}
                          onChange={(e) => updateSetting('notifications', 'smsStaffNotification', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Staff Notifications
                      </label>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsPromotional}
                          onChange={(e) => updateSetting('notifications', 'smsPromotional', e.target.checked)}
                          disabled={!settings.notifications.smsNotifications}
                        />
                        Promotional SMS
                      </label>
                    </div>
                  </div>
                </div>

                {/* General Notification Settings */}
                <div className={styles.settingsGroup}>
                  <h3>General Settings</h3>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>Reminder Hours Before Appointment</label>
                      <input
                        type="number"
                        value={settings.notifications.reminderHours}
                        onChange={(e) => updateSetting('notifications', 'reminderHours', parseInt(e.target.value))}
                        min="1"
                        max="168"
                      />
                      <small>How many hours before appointment to send reminders</small>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.adminNotifications}
                          onChange={(e) => updateSetting('notifications', 'adminNotifications', e.target.checked)}
                        />
                        Admin Notifications
                      </label>
                      <small>Receive notifications for admin events</small>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.customerNotifications}
                          onChange={(e) => updateSetting('notifications', 'customerNotifications', e.target.checked)}
                        />
                        Customer Notifications
                      </label>
                      <small>Send notifications to customers</small>
                    </div>
                  </div>
                </div>

                {/* Fallback Settings */}
                <div className={styles.settingsGroup}>
                  <h3>Fallback Behavior</h3>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.emailFallbackWhenSMSFails}
                          onChange={(e) => updateSetting('notifications', 'emailFallbackWhenSMSFails', e.target.checked)}
                        />
                        Send Email When SMS Fails
                      </label>
                      <small>Automatically send email if SMS delivery fails</small>
                    </div>
                    <div className={styles.settingItem}>
                      <label>
                        <input
                          type="checkbox"
                          checked={settings.notifications.smsFallbackWhenEmailFails}
                          onChange={(e) => updateSetting('notifications', 'smsFallbackWhenEmailFails', e.target.checked)}
                        />
                        Send SMS When Email Fails
                      </label>
                      <small>Automatically send SMS if email delivery fails</small>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className={styles.settingsSection}>
                <h2>Security Settings</h2>
                <p className={styles.comingSoon}>Security settings configuration coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
