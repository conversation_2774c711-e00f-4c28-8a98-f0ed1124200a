import { useState, useEffect } from 'react'
import Head from 'next/head' // Added Head
import { useAuth } from '@/hooks/useAuth'
import styles from '@/styles/admin/Tips.module.css'

/**
 * Tip Management Page
 * Allows admins to view, distribute, and manage tips for artists
 */
export default function TipManagement() {
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [tips, setTips] = useState([])
  const [summary, setSummary] = useState(null)
  const [selectedTips, setSelectedTips] = useState([])
  const [filter, setFilter] = useState('all')
  const [artistFilter, setArtistFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('')
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    if (!authLoading && user) {
      loadTips()
    }
  }, [authLoading, user, filter, artistFilter, dateFilter])

  const loadTips = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        status: filter,
        limit: '100'
      })

      if (artistFilter) params.append('artist_id', artistFilter)
      if (dateFilter) params.append('date_from', dateFilter)

      const response = await fetch(`/api/admin/tips?${params}`)
      const data = await response.json()

      if (response.ok) {
        setTips(data.tips || [])
        setSummary(data.summary || {})
      } else {
        console.error('Failed to load tips:', data.error)
      }
    } catch (error) {
      console.error('Error loading tips:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTipAction = async (tipId, action, distributionMethod = 'manual', notes = '') => {
    try {
      setProcessing(true)
      const response = await fetch('/api/admin/tips', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tip_id: tipId,
          action,
          distribution_method: distributionMethod,
          notes
        })
      })

      const data = await response.json()

      if (response.ok) {
        await loadTips() // Reload tips
        alert(`Tip ${action}d successfully!`)
      } else {
        alert(`Failed to ${action} tip: ${data.message}`)
      }
    } catch (error) {
      console.error(`Error ${action}ing tip:`, error)
      alert(`Error ${action}ing tip`)
    } finally {
      setProcessing(false)
    }
  }

  const handleBulkAction = async (action) => {
    if (selectedTips.length === 0) {
      alert('Please select tips to process')
      return
    }

    const distributionMethod = prompt('Distribution method (cash/bank_transfer/payroll):', 'cash')
    if (!distributionMethod) return

    const notes = prompt('Notes (optional):')

    try {
      setProcessing(true)
      const response = await fetch('/api/admin/tips', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: `bulk_${action}`,
          tip_ids: selectedTips,
          distribution_method: distributionMethod,
          notes
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSelectedTips([])
        await loadTips()
        alert(`${data.updated_count} tips ${action}d successfully!`)
      } else {
        alert(`Failed to ${action} tips: ${data.message}`)
      }
    } catch (error) {
      console.error(`Error bulk ${action}ing tips:`, error)
      alert(`Error bulk ${action}ing tips`)
    } finally {
      setProcessing(false)
    }
  }

  const toggleTipSelection = (tipId) => {
    setSelectedTips(prev => 
      prev.includes(tipId) 
        ? prev.filter(id => id !== tipId)
        : [...prev, tipId]
    )
  }

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount).toFixed(2)}`
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { class: 'pending', text: 'Pending' },
      distributed: { class: 'distributed', text: 'Distributed' },
      held: { class: 'held', text: 'On Hold' }
    }
    
    const config = statusConfig[status] || statusConfig.pending
    return (
      <span className={`${styles.statusBadge} ${styles[config.class]}`}>
        {config.text}
      </span>
    )
  }

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Tip Management - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading tip management...</p>
        </div>
      </>
    )
  }

  return (
    <>
      <Head>
        <title>Tip Management - Ocean Soul Sparkles Admin</title>
      </Head>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1>💰 Tip Management</h1>
          <p>Manage and distribute tips to artists and staff</p>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className={styles.summaryCards}>
            <div className={styles.summaryCard}>
              <h3>Total Tips</h3>
              <div className={styles.summaryValue}>{formatCurrency(summary.total_amount)}</div>
              <div className={styles.summaryCount}>{summary.total_tips} tips</div>
            </div>
            <div className={styles.summaryCard}>
              <h3>Pending Distribution</h3>
              <div className={styles.summaryValue}>{formatCurrency(summary.pending_amount)}</div>
              <div className={styles.summaryCount}>Awaiting distribution</div>
            </div>
            <div className={styles.summaryCard}>
              <h3>Distributed</h3>
              <div className={styles.summaryValue}>{formatCurrency(summary.distributed_amount)}</div>
              <div className={styles.summaryCount}>Already distributed</div>
            </div>
          </div>
        )}

        {/* Filters and Actions */}
        <div className={styles.controls}>
          <div className={styles.filters}>
            <select 
              value={filter} 
              onChange={(e) => setFilter(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Tips</option>
              <option value="pending">Pending</option>
              <option value="distributed">Distributed</option>
              <option value="held">On Hold</option>
            </select>

            <input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className={styles.dateFilter}
              placeholder="Filter by date"
            />
          </div>

          <div className={styles.bulkActions}>
            {selectedTips.length > 0 && (
              <>
                <button
                  onClick={() => handleBulkAction('distribute')}
                  disabled={processing}
                  className={styles.distributeButton}
                >
                  Distribute Selected ({selectedTips.length})
                </button>
                <button
                  onClick={() => handleBulkAction('hold')}
                  disabled={processing}
                  className={styles.holdButton}
                >
                  Hold Selected
                </button>
              </>
            )}
          </div>
        </div>

        {/* Tips Table */}
        <div className={styles.tipsTable}>
          {tips.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No tips found for the selected filters.</p>
            </div>
          ) : (
            <table>
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTips(tips.map(tip => tip.id))
                        } else {
                          setSelectedTips([])
                        }
                      }}
                      checked={selectedTips.length === tips.length && tips.length > 0}
                    />
                  </th>
                  <th>Date</th>
                  <th>Artist</th>
                  <th>Service</th>
                  <th>Amount</th>
                  <th>Method</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {tips.map(tip => (
                  <tr key={tip.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedTips.includes(tip.id)}
                        onChange={() => toggleTipSelection(tip.id)}
                      />
                    </td>
                    <td>{formatDate(tip.created_at)}</td>
                    <td>{tip.artist_profiles?.name || 'Unknown'}</td>
                    <td>{tip.bookings?.service_name || 'N/A'}</td>
                    <td className={styles.amount}>{formatCurrency(tip.amount)}</td>
                    <td>{tip.tip_method}</td>
                    <td>{getStatusBadge(tip.distribution_status)}</td>
                    <td>
                      <div className={styles.actionButtons}>
                        {tip.distribution_status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleTipAction(tip.id, 'distribute')}
                              disabled={processing}
                              className={styles.distributeBtn}
                            >
                              Distribute
                            </button>
                            <button
                              onClick={() => handleTipAction(tip.id, 'hold')}
                              disabled={processing}
                              className={styles.holdBtn}
                            >
                              Hold
                            </button>
                          </>
                        )}
                        {tip.distribution_status === 'held' && (
                          <button
                            onClick={() => handleTipAction(tip.id, 'release')}
                            disabled={processing}
                            className={styles.releaseBtn}
                          >
                            Release
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </>
  )
}
