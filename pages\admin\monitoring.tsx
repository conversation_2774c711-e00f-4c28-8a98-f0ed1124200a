/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring Page
 * Admin interface for viewing performance metrics and alerts
 */

import React from 'react';
import <PERSON> from 'next/head';
import PerformanceDashboard from '../../components/admin/PerformanceDashboard';

const MonitoringPage: React.FC = () => {
  // The loading and user states, checkAuth, and handleLogout functions are removed.
  // The global AdminLayout and useAuth hook will handle authentication and loading states.

  // The loading and !user checks are removed.
  // If this page needs specific loading/error states for its content (PerformanceDashboard),
  // they should be handled within PerformanceDashboard or added here if necessary,
  // but not for the overall page auth/layout.

  return (
    <>
      <Head>
        <title>Performance Monitoring - Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Performance monitoring and system health dashboard" />
      </Head>
      {/* The custom header, nav, main, and footer structure is removed. */}
      {/* The styles.container div is also removed. */}
      <PerformanceDashboard />
    </>
  );
};

export default MonitoringPage;
