import { useState, useEffect } from 'react';
import styles from '../../styles/admin/BookingAnalytics.module.css';

interface Booking {
  id: string;
  customer_name: string;
  service_name: string;
  artist_name: string;
  start_time: string;
  end_time: string;
  status: string;
  total_amount: number;
  created_at: string;
}

interface BookingAnalyticsProps {
  bookings: Booking[];
}

interface MinimalAnalyticsData {
  todayBookings: number;
  tomorrowBookings: number;
  thisWeekBookings: number;
  thisMonthBookings: number;
  thisWeekRevenue: number;
  thisMonthRevenue: number;
}

export default function BookingAnalytics({ bookings }: BookingAnalyticsProps) {
  const [analytics, setAnalytics] = useState<MinimalAnalyticsData | null>(null);

  useEffect(() => {
    calculateMinimalAnalytics();
  }, [bookings]);

  const calculateMinimalAnalytics = () => {
    if (!bookings || bookings.length === 0) {
      setAnalytics({
        todayBookings: 0,
        tomorrowBookings: 0,
        thisWeekBookings: 0,
        thisMonthBookings: 0,
        thisWeekRevenue: 0,
        thisMonthRevenue: 0
      });
      return;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfterTomorrow = new Date(tomorrow);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);

    // Calculate week boundaries (Monday to Sunday)
    const startOfWeek = new Date(today);
    const dayOfWeek = today.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, Monday = 1
    startOfWeek.setDate(today.getDate() - daysToMonday);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    // Calculate month boundaries
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);

    // Filter bookings for each time period
    const todayBookings = bookings.filter(booking => {
      const bookingDate = new Date(booking.start_time || booking.created_at);
      return bookingDate >= today && bookingDate < tomorrow;
    });

    const tomorrowBookings = bookings.filter(booking => {
      const bookingDate = new Date(booking.start_time || booking.created_at);
      return bookingDate >= tomorrow && bookingDate < dayAfterTomorrow;
    });

    const thisWeekBookings = bookings.filter(booking => {
      const bookingDate = new Date(booking.start_time || booking.created_at);
      return bookingDate >= startOfWeek && bookingDate <= endOfWeek;
    });

    const thisMonthBookings = bookings.filter(booking => {
      const bookingDate = new Date(booking.start_time || booking.created_at);
      return bookingDate >= startOfMonth && bookingDate <= endOfMonth;
    });

    // Calculate revenue for time periods
    const thisWeekRevenue = thisWeekBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
    const thisMonthRevenue = thisMonthBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);

    setAnalytics({
      todayBookings: todayBookings.length,
      tomorrowBookings: tomorrowBookings.length,
      thisWeekBookings: thisWeekBookings.length,
      thisMonthBookings: thisMonthBookings.length,
      thisWeekRevenue,
      thisMonthRevenue
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  if (!analytics) {
    return (
      <div className={styles.analyticsContainer}>
        <div className={styles.emptyState}>
          <p>No booking data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.analyticsContainer}>
      <div className={styles.analyticsHeader}>
        <h3>Booking Overview</h3>
        <div className={styles.headerNote}>
          <span>Essential booking metrics for daily operations</span>
          <a href="/admin/reports" className={styles.reportsLink}>
            View Detailed Analytics →
          </a>
        </div>
      </div>

      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.todayBookings}</div>
          <div className={styles.metricLabel}>Today</div>
          <div className={styles.metricSubtext}>Bookings scheduled</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.tomorrowBookings}</div>
          <div className={styles.metricLabel}>Tomorrow</div>
          <div className={styles.metricSubtext}>Upcoming bookings</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.thisWeekBookings}</div>
          <div className={styles.metricLabel}>This Week</div>
          <div className={styles.metricSubtext}>{formatCurrency(analytics.thisWeekRevenue)} revenue</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.thisMonthBookings}</div>
          <div className={styles.metricLabel}>This Month</div>
          <div className={styles.metricSubtext}>{formatCurrency(analytics.thisMonthRevenue)} revenue</div>
        </div>
      </div>

    </div>
  );
}
