import { useState, useEffect } from 'react'
import Head from 'next/head'
import ReceiptCustomizer from '@/components/admin/pos/ReceiptCustomizer'
import { useAuth } from '@/hooks/useAuth'
import styles from '@/styles/admin/Receipts.module.css'

/**
 * Receipt Management Page
 * Allows admins to customize receipt templates and manage receipt settings
 */
export default function ReceiptsPage() {
  const { user, loading: authLoading } = useAuth()
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [activeTab, setActiveTab] = useState('templates') // 'templates', 'settings'
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState(null)

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
  }

  const showMessage = (text, type = 'success') => {
    setMessage({ text, type })
    setTimeout(() => setMessage(null), 5000)
  }

  if (authLoading) {
    return (
      <>
        <Head>
          <title>Loading Receipt Management - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading...</p>
        </div>
      </>
    )
  }

  return (
    <>
      <Head>
        <title>Receipt Management - Ocean Soul Sparkles Admin</title>
      </Head>

      <div className={styles.receiptsPage}>
        <div className={styles.header}>
          <div className={styles.headerContent}>
            <h1>Receipt Management</h1>
            <p>Customize receipt templates and manage receipt settings for your POS system.</p>
          </div>
        </div>

        {message && (
          <div className={`${styles.message} ${styles[message.type]}`}>
            {message.text}
          </div>
        )}

        <div className={styles.tabNavigation}>
          <button
            className={`${styles.tabButton} ${activeTab === 'templates' ? styles.active : ''}`}
            onClick={() => setActiveTab('templates')}
          >
            📄 Templates
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'settings' ? styles.active : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            ⚙️ Settings
          </button>
        </div>

        <div className={styles.tabContent}>
          {activeTab === 'templates' && (
            <div className={styles.templatesTab}>
              <div className={styles.tabHeader}>
                <h2>Receipt Templates</h2>
                <p>Choose and customize receipt templates for different transaction types.</p>
              </div>
              
              <ReceiptCustomizer
                onTemplateSelect={handleTemplateSelect}
                selectedTemplate={selectedTemplate}
                showPreview={true}
              />
            </div>
          )}

          {activeTab === 'settings' && (
            <div className={styles.settingsTab}>
              <div className={styles.tabHeader}>
                <h2>Receipt Settings</h2>
                <p>Configure global receipt settings and business information.</p>
              </div>
              
              <ReceiptSettings onMessage={showMessage} />
            </div>
          )}
        </div>
      </div>
    </>
  )
}

/**
 * Receipt Settings Component
 * Manages global receipt settings
 */
function ReceiptSettings({ onMessage }) {
  const [settings, setSettings] = useState({
    autoGenerateReceipts: true,
    emailReceiptsToCustomers: true,
    printReceiptsAutomatically: false,
    receiptNumberPrefix: 'OSS',
    includeQRCode: false,
    defaultTemplate: '',
    businessInfo: {
      name: 'Ocean Soul Sparkles',
      address: '',
      phone: '',
      email: '',
      website: '',
      abn: ''
    }
  })
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState([])

  useEffect(() => {
    loadSettings()
    loadTemplates()
  }, [])

  const loadSettings = async () => {
    try {
      // Load settings from system_settings table
      const response = await fetch('/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        // Extract receipt-related settings
        const receiptSettings = {
          autoGenerateReceipts: data.receipt?.autoGenerateReceipts !== 'false',
          emailReceiptsToCustomers: data.receipt?.emailReceiptsToCustomers !== 'false',
          printReceiptsAutomatically: data.receipt?.printReceiptsAutomatically === 'true',
          receiptNumberPrefix: data.receipt?.receiptNumberPrefix || 'OSS',
          includeQRCode: data.receipt?.includeQRCode === 'true',
          defaultTemplate: data.receipt?.defaultTemplate || '',
          businessInfo: {
            name: data.general?.businessName || 'Ocean Soul Sparkles',
            address: data.general?.businessAddress || '',
            phone: data.general?.businessPhone || '',
            email: data.general?.businessEmail || '',
            website: data.general?.businessWebsite || '',
            abn: data.general?.businessABN || ''
          }
        }
        setSettings(receiptSettings)
      }
    } catch (error) {
      console.error('Error loading receipt settings:', error)
    }
  }

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/admin/receipts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTemplates(data.templates || [])
      }
    } catch (error) {
      console.error('Error loading templates:', error)
    }
  }

  const handleSettingChange = (key, value) => {
    if (key.includes('.')) {
      const [parent, child] = key.split('.')
      setSettings(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setSettings(prev => ({
        ...prev,
        [key]: value
      }))
    }
  }

  const saveSettings = async () => {
    try {
      setLoading(true)
      
      // Save settings to system_settings table
      const settingsToSave = {
        'receipt.autoGenerateReceipts': settings.autoGenerateReceipts.toString(),
        'receipt.emailReceiptsToCustomers': settings.emailReceiptsToCustomers.toString(),
        'receipt.printReceiptsAutomatically': settings.printReceiptsAutomatically.toString(),
        'receipt.receiptNumberPrefix': settings.receiptNumberPrefix,
        'receipt.includeQRCode': settings.includeQRCode.toString(),
        'receipt.defaultTemplate': settings.defaultTemplate,
        'general.businessName': settings.businessInfo.name,
        'general.businessAddress': settings.businessInfo.address,
        'general.businessPhone': settings.businessInfo.phone,
        'general.businessEmail': settings.businessInfo.email,
        'general.businessWebsite': settings.businessInfo.website,
        'general.businessABN': settings.businessInfo.abn
      }

      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({ settings: settingsToSave })
      })

      if (!response.ok) {
        throw new Error('Failed to save settings')
      }

      onMessage?.('Receipt settings saved successfully!', 'success')
    } catch (error) {
      console.error('Error saving settings:', error)
      onMessage?.('Failed to save settings. Please try again.', 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={styles.receiptSettings}>
      <div className={styles.settingsGrid}>
        <div className={styles.settingsSection}>
          <h3>Receipt Generation</h3>
          <div className={styles.settingGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={settings.autoGenerateReceipts}
                onChange={(e) => handleSettingChange('autoGenerateReceipts', e.target.checked)}
              />
              <span>Automatically generate receipts for all transactions</span>
            </label>
          </div>
          
          <div className={styles.settingGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={settings.emailReceiptsToCustomers}
                onChange={(e) => handleSettingChange('emailReceiptsToCustomers', e.target.checked)}
              />
              <span>Email receipts to customers automatically</span>
            </label>
          </div>
          
          <div className={styles.settingGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={settings.printReceiptsAutomatically}
                onChange={(e) => handleSettingChange('printReceiptsAutomatically', e.target.checked)}
              />
              <span>Print receipts automatically</span>
            </label>
          </div>
        </div>

        <div className={styles.settingsSection}>
          <h3>Receipt Configuration</h3>
          <div className={styles.settingGroup}>
            <label>Receipt Number Prefix</label>
            <input
              type="text"
              value={settings.receiptNumberPrefix}
              onChange={(e) => handleSettingChange('receiptNumberPrefix', e.target.value)}
              placeholder="OSS"
              maxLength={10}
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>Default Template</label>
            <select
              value={settings.defaultTemplate}
              onChange={(e) => handleSettingChange('defaultTemplate', e.target.value)}
            >
              <option value="">Select default template</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>
                  {template.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className={styles.settingGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={settings.includeQRCode}
                onChange={(e) => handleSettingChange('includeQRCode', e.target.checked)}
              />
              <span>Include QR code on receipts</span>
            </label>
          </div>
        </div>

        <div className={styles.settingsSection}>
          <h3>Business Information</h3>
          <div className={styles.settingGroup}>
            <label>Business Name</label>
            <input
              type="text"
              value={settings.businessInfo.name}
              onChange={(e) => handleSettingChange('businessInfo.name', e.target.value)}
              placeholder="Ocean Soul Sparkles"
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>Address</label>
            <textarea
              value={settings.businessInfo.address}
              onChange={(e) => handleSettingChange('businessInfo.address', e.target.value)}
              placeholder="Business address"
              rows={3}
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>Phone</label>
            <input
              type="text"
              value={settings.businessInfo.phone}
              onChange={(e) => handleSettingChange('businessInfo.phone', e.target.value)}
              placeholder="+61 XXX XXX XXX"
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>Email</label>
            <input
              type="email"
              value={settings.businessInfo.email}
              onChange={(e) => handleSettingChange('businessInfo.email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>Website</label>
            <input
              type="text"
              value={settings.businessInfo.website}
              onChange={(e) => handleSettingChange('businessInfo.website', e.target.value)}
              placeholder="oceansoulsparkles.com.au"
            />
          </div>
          
          <div className={styles.settingGroup}>
            <label>ABN</label>
            <input
              type="text"
              value={settings.businessInfo.abn}
              onChange={(e) => handleSettingChange('businessInfo.abn', e.target.value)}
              placeholder="**************"
            />
          </div>
        </div>
      </div>

      <div className={styles.settingsActions}>
        <button
          onClick={saveSettings}
          disabled={loading}
          className={styles.saveButton}
        >
          {loading ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </div>
  )
}
