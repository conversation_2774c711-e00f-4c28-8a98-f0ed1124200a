import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

// Handle missing environment variables gracefully
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder';

const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Get dashboard data based on user role
    const dashboardData = await getDashboardData(user.role, user.id);

    return res.status(200).json(dashboardData);

  } catch (error) {
    console.error('Dashboard API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function getDashboardData(userRole: string, userId: string) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

  try {
    // Base stats for all users
    const stats = {
      totalBookings: 0,
      totalRevenue: 0,
      activeCustomers: 0,
      pendingBookings: 0,
      completedBookings: 0,
      cancelledBookings: 0,
      averageBookingValue: 0,
      monthlyGrowth: 0
    };

    // Recent bookings
    let recentBookings = [];

    // Recent activity
    let recentActivity = [];

    // Artist-specific stats
    let artistStats = null;

    // System stats (admin only)
    let systemStats = null;

    if (userRole === 'DEV' || userRole === 'Admin') {
      // Admin dashboard - full access
      
      // Get booking stats
      const { data: bookings } = await supabase
        .from('bookings')
        .select('*')
        .gte('created_at', startOfMonth.toISOString());

      if (bookings) {
        stats.totalBookings = bookings.length;
        stats.totalRevenue = bookings
          .filter(b => b.status === 'completed')
          .reduce((sum, b) => sum + (b.total_amount || 0), 0);
        stats.pendingBookings = bookings.filter(b => b.status === 'pending').length;
        stats.completedBookings = bookings.filter(b => b.status === 'completed').length;
        stats.cancelledBookings = bookings.filter(b => b.status === 'cancelled').length;
        stats.averageBookingValue = stats.totalBookings > 0 ? 
          stats.totalRevenue / stats.completedBookings : 0;
      }

      // Get customer count (active in last 30 days)
      const { count: customerCount } = await supabase
        .from('customers')
        .select('*', { count: 'exact', head: true })
        .gte('updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      stats.activeCustomers = customerCount || 0;

      // Get recent bookings with proper joins
      const { data: recentBookingsData } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          booking_time,
          status,
          total_amount,
          created_at,
          customer_id,
          service_id,
          artist_id,
          customers!inner(id, first_name, last_name, email, phone, created_at),
          services!inner(id, name, duration),
          user_profiles!artist_id(id, name)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      // Get booking counts for each customer in recent bookings
      const customerIds = recentBookingsData?.map(b => b.customer_id) || [];
      const { data: customerBookingCounts } = await supabase
        .from('bookings')
        .select('customer_id')
        .in('customer_id', customerIds);

      // Create booking count map
      const bookingCountMap = {};
      customerBookingCounts?.forEach(booking => {
        bookingCountMap[booking.customer_id] = (bookingCountMap[booking.customer_id] || 0) + 1;
      });

      // Enhance recent bookings with customer booking counts
      recentBookings = (recentBookingsData || []).map(booking => ({
        ...booking,
        customers: {
          ...booking.customers,
          total_bookings: bookingCountMap[booking.customer_id] || 0
        }
      }));

      // System stats
      systemStats = {
        avgResponseTime: 150, // Mock data
        activeUsers: 5,
        systemHealth: 'healthy'
      };

    } else if (userRole === 'Artist' || userRole === 'Braider') {
      // Artist/Braider dashboard - limited to their data
      
      // Get artist's bookings
      const { data: artistBookings } = await supabase
        .from('bookings')
        .select('*')
        .eq('artist_id', userId)
        .gte('created_at', startOfMonth.toISOString());

      if (artistBookings) {
        stats.totalBookings = artistBookings.length;
        stats.totalRevenue = artistBookings
          .filter(b => b.status === 'completed')
          .reduce((sum, b) => sum + (b.total_amount || 0), 0);
        stats.pendingBookings = artistBookings.filter(b => b.status === 'pending').length;
        stats.completedBookings = artistBookings.filter(b => b.status === 'completed').length;
        stats.cancelledBookings = artistBookings.filter(b => b.status === 'cancelled').length;
      }

      // Artist-specific stats
      const { data: weeklyBookings } = await supabase
        .from('bookings')
        .select('*')
        .eq('artist_id', userId)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      const { data: monthlyBookings } = await supabase
        .from('bookings')
        .select('*')
        .eq('artist_id', userId)
        .gte('created_at', startOfMonth.toISOString())
        .eq('status', 'completed');

      artistStats = {
        weeklyBookings: weeklyBookings?.length || 0,
        monthlyEarnings: monthlyBookings?.reduce((sum, b) => sum + (b.total_amount || 0), 0) || 0,
        averageRating: 4.8 // Mock data - would calculate from reviews
      };

      // Get artist's recent bookings
      const { data: artistRecentBookings } = await supabase
        .from('bookings')
        .select(`
          *,
          customers (first_name, last_name),
          services (name)
        `)
        .eq('artist_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      recentBookings = artistRecentBookings || [];
    }

    // Calculate monthly growth (mock calculation)
    const { data: lastMonthBookings } = await supabase
      .from('bookings')
      .select('total_amount')
      .gte('created_at', startOfLastMonth.toISOString())
      .lte('created_at', endOfLastMonth.toISOString())
      .eq('status', 'completed');

    const lastMonthRevenue = lastMonthBookings?.reduce((sum, b) => sum + (b.total_amount || 0), 0) || 0;
    if (lastMonthRevenue > 0) {
      stats.monthlyGrowth = ((stats.totalRevenue - lastMonthRevenue) / lastMonthRevenue) * 100;
    }

    // Mock recent activity data
    recentActivity = [
      {
        id: '1',
        type: 'booking',
        title: 'New booking created',
        description: 'Sarah Johnson booked Festival Face Paint',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        user: 'Emma Wilson'
      },
      {
        id: '2',
        type: 'payment',
        title: 'Payment received',
        description: 'Payment of $120 received',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        user: 'System'
      },
      {
        id: '3',
        type: 'customer',
        title: 'New customer registered',
        description: 'Mike Chen created account',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        user: 'Mike Chen'
      }
    ];

    return {
      stats,
      recentBookings,
      recentActivity,
      artistStats,
      systemStats
    };

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    
    // Return mock data on error
    return {
      stats: {
        totalBookings: 0,
        totalRevenue: 0,
        activeCustomers: 0,
        pendingBookings: 0,
        completedBookings: 0,
        cancelledBookings: 0,
        averageBookingValue: 0,
        monthlyGrowth: 0
      },
      recentBookings: [],
      recentActivity: [],
      artistStats: null,
      systemStats: null
    };
  }
}
