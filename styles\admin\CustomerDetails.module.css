/* Customer Details Page Styles - Consistent Design System */

.customerDetailsContainer {
  min-height: 100vh;
  background: var(--admin-bg-secondary);
  font-family: inherit;
}

.header {
  background: var(--admin-bg-primary);
  padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px var(--admin-shadow-light);
  border-bottom: 1px solid var(--admin-border-light);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
}

.breadcrumb a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.breadcrumb a:hover {
  color: #764ba2;
}

.breadcrumb span {
  color: #94a3b8;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.editButton, .bookButton, .deleteButton, .backButton {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  font-size: 0.875rem;
}

.editButton {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.bookButton {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.deleteButton {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.backButton {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
}

.editButton:hover, .bookButton:hover, .deleteButton:hover, .backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.customerContent {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.mainInfo {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.customerHeader {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.customerAvatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  text-transform: uppercase;
}

.customerName h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.customerEmail {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detailCard {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.detailCard h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.contactInfo, .personalInfo {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contactItem, .infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.contactItem strong, .infoItem strong {
  color: #374151;
  font-weight: 600;
}

.notes {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
  font-size: 0.875rem;
}

.metaInfo {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
  color: #64748b;
  font-size: 0.875rem;
}

.metaItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.quickActions, .bookingHistory {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quickActions h3, .bookingHistory h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.actionButton {
  display: block;
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.actionButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

.noBookings {
  color: #64748b;
  font-style: italic;
  text-align: center;
  padding: 2rem 0;
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bookingItem {
  background: #f8fafc;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.serviceName {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.bookingStatus {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.bookingDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #64748b;
}

.viewAllBookings {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.viewAllBookings:hover {
  color: #764ba2;
}

/* Loading and Error States */
.loadingContainer, .errorContainer, .notFoundContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h2, .notFoundContainer h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .customerContent {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .headerActions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .customerHeader {
    flex-direction: column;
    text-align: center;
  }
  
  .detailsGrid {
    grid-template-columns: 1fr;
  }
  
  .metaInfo {
    flex-direction: column;
    gap: 1rem;
  }
}
