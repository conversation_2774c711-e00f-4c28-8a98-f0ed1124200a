import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import NewBooking from '../../../../pages/admin/bookings/new'; // Adjust path as needed
import { useAuth } from '../../../../hooks/useAuth';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';

// Mock dependencies
jest.mock('../../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('NewBooking Page', () => {
  let mockRouterPush;

  beforeEach(() => {
    // Reset mocks for each test
    jest.clearAllMocks();

    // Default mock for useAuth
    useAuth.mockReturnValue({
      user: { id: 'admin123', role: 'Admin' },
      loading: false,
    });

    // Default mock for useRouter
    mockRouterPush = jest.fn();
    useRouter.mockReturnValue({
      query: {},
      push: mockRouterPush,
    });

    // Default mock for fetch (successful initial data load)
    global.fetch.mockImplementation((url) => {
      if (url.startsWith('/api/admin/customers')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ customers: [{ id: 'cust1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>' }] }),
        });
      }
      if (url.startsWith('/api/admin/services') && !url.includes('/tiers')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ services: [{ id: 'serv1', name: 'Service A', base_price: 100, price: 100 }] }), // Added price for display
        });
      }
      if (url.startsWith('/api/admin/artists')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ artists: [{ id: 'art1', name: 'Artist X', artist_name: 'Artist X' }] }), // Added artist_name for display
        });
      }
      if (url.includes('/tiers')) { // For service tiers
        return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ tiers: [{ id: 'tier1', name: 'Standard Tier', price: 100, duration: 60, is_default: true }] }),
        });
      }
      // Default for booking creation POST - will be overridden in tests
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ booking: { id: 'book123' } }),
      });
    });
  });

  // Test case 1: "should include tier_id in bookingData when a tier is selected"
  test('should include tier_id in bookingData when a tier is selected and form is submitted', async () => {
    render(<NewBooking />);

    // Wait for initial data to load (customers, services, artists)
    // Using findBy queries as they wait for elements to appear
    await screen.findByText('Select a customer...');

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/Customer/i), { target: { value: 'cust1' } });
    fireEvent.change(screen.getByLabelText(/^Service \*/i), { target: { value: 'serv1' } });

    // Wait for tiers to load and be selected (default tier)
    await waitFor(() => {
        // Using findByLabelText or checking if the specific option is rendered
        expect(screen.getByLabelText(/Service Tier/i)).toBeInTheDocument();
        expect(screen.getByDisplayValue('Standard Tier - $100 (60 min)')).toBeInTheDocument(); // Check if default tier is selected
        expect(screen.getByLabelText(/Service Tier/i).value).toBe('tier1');
    });

    fireEvent.change(screen.getByLabelText(/Artist/i), { target: { value: 'art1' } });
    // Set a future date to pass validation
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);
    const dateString = futureDate.toISOString().split('T')[0];
    fireEvent.change(screen.getByLabelText(/Date/i), { target: { value: dateString } });
    fireEvent.change(screen.getByLabelText(/Start Time/i), { target: { value: '10:00' } });

    // Mock a successful booking response specifically for this POST
    const mockPostFetch = jest.fn((url, options) => {
        if (options.method === 'POST' && url.endsWith('/api/admin/bookings')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({ booking: { id: 'bookNew123' } }),
            });
        }
        // Fallback to the general fetch mock for other calls if necessary
        return global.fetch(url, options);
    });
    global.fetch = mockPostFetch;

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Create Booking/i }));

    // Check if fetch was called with tier_id for the booking creation
    await waitFor(() => {
      expect(mockPostFetch).toHaveBeenCalledWith(
        '/api/admin/bookings',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('"tier_id":"tier1"'), // Check that tier_id is in the JSON string
        })
      );
    });

    expect(toast.success).toHaveBeenCalledWith('Booking created successfully!');
    expect(mockRouterPush).toHaveBeenCalledWith('/admin/bookings/bookNew123');
  });

  // Test case 2: "should not include tier_id if service has no tiers or no tier is selected"
  test('should submit booking data without tier_id if service has no tiers', async () => {
    // Mock fetch for service tiers to return an empty array
    const specificFetchMock = (url) => {
      if (url.startsWith('/api/admin/customers')) return Promise.resolve({ ok: true, json: () => Promise.resolve({ customers: [{ id: 'cust1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>' }] }) });
      if (url.startsWith('/api/admin/services/serv2/tiers')) { // Service with no tiers
        return Promise.resolve({ ok: true, json: () => Promise.resolve({ tiers: [] }) });
      }
      if (url.startsWith('/api/admin/services') && !url.includes('/tiers')) { // All services including serv2
         return Promise.resolve({ ok: true, json: () => Promise.resolve({ services: [
             { id: 'serv1', name: 'Service A', base_price: 100, price: 100 },
             { id: 'serv2', name: 'Service B', base_price: 50, price: 50 }
         ]})});
      }
      if (url.startsWith('/api/admin/artists')) return Promise.resolve({ ok: true, json: () => Promise.resolve({ artists: [{ id: 'art1', name: 'Artist Y', artist_name: 'Artist Y' }] }) });

      // This part will handle the POST request for booking creation
      if (url.endsWith('/api/admin/bookings') && arguments[1]?.method === 'POST') {
        return Promise.resolve({ ok: true, json: () => Promise.resolve({ booking: { id: 'bookNoTierNew123' } }) });
      }
      // Fallback for any other GET for tiers (e.g. for serv1 if it was somehow selected first)
      if (url.includes('/tiers')) {
        return Promise.resolve({ ok: true, json: () => Promise.resolve({ tiers: [{ id: 'tier1', name: 'Standard Tier', price: 100, duration: 60, is_default: true }] }) });
      }
      return Promise.reject(new Error(`Unhandled fetch call: ${url}`)); // Should not happen
    };
    global.fetch = jest.fn(specificFetchMock);


    render(<NewBooking />);
    await screen.findByText('Select a customer...');

    fireEvent.change(screen.getByLabelText(/Customer/i), { target: { value: 'cust1' } });
    fireEvent.change(screen.getByLabelText(/^Service \*/i), { target: { value: 'serv2' } }); // Select service that will have no tiers

    // Ensure tier dropdown does not appear
    await waitFor(() => {
      expect(screen.queryByLabelText(/Service Tier/i)).not.toBeInTheDocument();
    });

    fireEvent.change(screen.getByLabelText(/Artist/i), { target: { value: 'art1' } });
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 8); // Different future date
    const dateString = futureDate.toISOString().split('T')[0];
    fireEvent.change(screen.getByLabelText(/Date/i), { target: { value: dateString } });
    fireEvent.change(screen.getByLabelText(/Start Time/i), { target: { value: '11:00' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Create Booking/i }));

    await waitFor(() => {
      const fetchCalls = global.fetch.mock.calls;
      const postCall = fetchCalls.find(call => call[0] === '/api/admin/bookings' && call[1].method === 'POST');
      expect(postCall).toBeDefined();
      if (postCall) { // Type guard
        const body = JSON.parse(postCall[1].body);
        expect(body).not.toHaveProperty('tier_id');
        expect(body).not.toHaveProperty('tier_name');
        expect(body).not.toHaveProperty('tier_price');
        // tier_id might be sent as null or undefined by default if the field exists in formData,
        // the API should handle this. The key is that it's not a *specific tier's ID*.
        // For this test, we ensure it's not in the payload if not selected.
        // If the component explicitly sets it to null/undefined, that's also acceptable.
        // The critical part is that no actual tier_id string is sent.
        expect(body.tier_id).toBeUndefined();
      }
    });
    expect(toast.success).toHaveBeenCalledWith('Booking created successfully!');
    expect(mockRouterPush).toHaveBeenCalledWith('/admin/bookings/bookNoTierNew123');
  });

});
