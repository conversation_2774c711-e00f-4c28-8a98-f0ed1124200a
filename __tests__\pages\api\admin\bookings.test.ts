import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../pages/api/admin/bookings'; // Adjust path as necessary
import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

// Mock Supabase
const mockQueryBuilder = {
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  gt: jest.fn().mockReturnThis(),
  lt: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  or: jest.fn().mockReturnThis(),
  single: jest.fn(),
  order: jest.fn().mockReturnThis(), // Added for GET requests if testing them later
};
const mockSupabaseClient = {
  from: jest.fn(() => mockQueryBuilder),
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}));

// Mock admin token verification
jest.mock('../../../../lib/auth/admin-auth', () => ({
  verifyAdminToken: jest.fn(),
}));

// Helper to create mock API request and response
const createMockApiContext = (method: string, body?: any, query?: any, headers?: any, cookies?: any) => {
  const req = {
    method,
    body: body || {},
    query: query || {},
    headers: headers || { authorization: 'Bearer test-token' }, // Default auth header
    cookies: cookies || { 'admin-token': 'test-token' }, // Default auth cookie
  } as unknown as NextApiRequest;

  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    setHeader: jest.fn(),
  } as unknown as NextApiResponse;
  return { req, res };
};

describe('Admin Bookings API', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    (verifyAdminToken as jest.Mock).mockResolvedValue({ valid: true, user: { id: 'admin-user-id', role: 'Admin' } });
  });

  describe('POST /api/admin/bookings (handleCreateBooking)', () => {
    it('should create a booking successfully with valid data including tier_id', async () => {
      const mockCustomer = { id: 'cust_123', first_name: 'John', last_name: 'Doe' };
      const mockService = { id: 'serv_456', name: 'Test Service', base_price: 100 };
      const mockArtist = { id: 'art_789', artist_name: 'Test Artist' };
      const mockBookingData = {
        id: 'book_new',
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        status: 'confirmed',
        total_amount: 120,
        notes: 'Test booking',
        location: 'Studio',
        tier_id: 'tier_abc',
        tier_name: 'Gold Tier',
        tier_price: 120,
        booking_source: 'admin',
        created_at: new Date().toISOString(),
        booking_date: '2024-08-15', // Added based on select fields in API
      };

      // Mock Supabase responses
      mockQueryBuilder.single
        .mockResolvedValueOnce({ data: mockCustomer, error: null }) // customer check
        .mockResolvedValueOnce({ data: mockService, error: null })  // service check
        .mockResolvedValueOnce({ data: mockArtist, error: null })   // artist check
        .mockResolvedValueOnce({ data: { ...mockBookingData }, error: null }); // insert booking

      mockQueryBuilder.select // For conflict check
        .mockResolvedValueOnce({ data: [], error: null });


      const { req, res } = createMockApiContext('POST', {
        customer_id: mockBookingData.customer_id,
        service_id: mockBookingData.service_id,
        assigned_artist_id: mockBookingData.assigned_artist_id,
        start_time: mockBookingData.start_time,
        end_time: mockBookingData.end_time,
        total_amount: mockBookingData.total_amount,
        notes: mockBookingData.notes,
        location: mockBookingData.location,
        tier_id: mockBookingData.tier_id,
        tier_name: mockBookingData.tier_name,
        tier_price: mockBookingData.tier_price,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Booking created successfully',
        booking: expect.objectContaining({
          id: mockBookingData.id,
          customer_id: mockBookingData.customer_id,
          service_id: mockBookingData.service_id,
          assigned_artist_id: mockBookingData.assigned_artist_id,
          tier_id: mockBookingData.tier_id,
          tier_name: mockBookingData.tier_name,
          tier_price: mockBookingData.tier_price,
          customer: mockCustomer,
          service: mockService,
          artist: mockArtist,
        }),
      }));

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('bookings');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          customer_id: mockBookingData.customer_id,
          service_id: mockBookingData.service_id,
          assigned_artist_id: mockBookingData.assigned_artist_id,
          start_time: mockBookingData.start_time,
          end_time: mockBookingData.end_time,
          status: 'confirmed', // default
          total_amount: mockBookingData.total_amount,
          notes: mockBookingData.notes,
          location: mockBookingData.location,
          tier_id: mockBookingData.tier_id,
          tier_name: mockBookingData.tier_name,
          tier_price: mockBookingData.tier_price,
          booking_source: 'admin', // default
        }),
      ]);
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(expect.stringContaining('tier_id'));
    });

    it('should create a booking successfully without tier information', async () => {
      const mockCustomer = { id: 'cust_123', first_name: 'John', last_name: 'Doe' };
      const mockService = { id: 'serv_456', name: 'Test Service', base_price: 100 };
      const mockArtist = { id: 'art_789', artist_name: 'Test Artist' };
      const mockBookingData = {
        id: 'book_new_no_tier',
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-16T10:00:00.000Z',
        end_time: '2024-08-16T11:00:00.000Z',
        status: 'confirmed',
        total_amount: 100, // No tier price
        notes: 'Test booking without tier',
        location: 'Studio',
        booking_source: 'admin',
        created_at: new Date().toISOString(),
        booking_date: '2024-08-16',
      };

      // Mock Supabase responses
      (mockQueryBuilder.single as jest.Mock)
        .mockResolvedValueOnce({ data: mockCustomer, error: null }) // customer check
        .mockResolvedValueOnce({ data: mockService, error: null })  // service check
        .mockResolvedValueOnce({ data: mockArtist, error: null })   // artist check
        .mockResolvedValueOnce({ data: { ...mockBookingData, tier_id: undefined, tier_name: undefined, tier_price: undefined }, error: null }); // insert booking

      (mockQueryBuilder.select as jest.Mock) // For conflict check
        .mockResolvedValueOnce({ data: [], error: null });


      const { req, res } = createMockApiContext('POST', {
        customer_id: mockBookingData.customer_id,
        service_id: mockBookingData.service_id,
        assigned_artist_id: mockBookingData.assigned_artist_id,
        start_time: mockBookingData.start_time,
        end_time: mockBookingData.end_time,
        total_amount: mockBookingData.total_amount, // Ensure this is the base price if no tier
        notes: mockBookingData.notes,
        location: mockBookingData.location,
        // No tier_id, tier_name, or tier_price
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Booking created successfully',
        booking: expect.objectContaining({
          id: mockBookingData.id,
          customer_id: mockBookingData.customer_id,
          service_id: mockBookingData.service_id,
          assigned_artist_id: mockBookingData.assigned_artist_id,
          tier_id: undefined,
          tier_name: undefined,
          tier_price: undefined,
          customer: mockCustomer,
          service: mockService,
          artist: mockArtist,
        }),
      }));

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('bookings');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          customer_id: mockBookingData.customer_id,
          service_id: mockBookingData.service_id,
          assigned_artist_id: mockBookingData.assigned_artist_id,
          start_time: mockBookingData.start_time,
          end_time: mockBookingData.end_time,
          total_amount: mockBookingData.total_amount,
          tier_id: undefined, // Explicitly check for undefined
          tier_name: undefined,
          tier_price: undefined,
        }),
      ]);
      // Ensure tier_id is still in the select statement, as the column might exist in DB
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(expect.stringContaining('tier_id'));
    });

    it('should return 400 if tier_id is missing but tier_name is provided', async () => {
      const { req, res } = createMockApiContext('POST', {
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 120,
        tier_name: 'Gold Tier', // tier_name provided
        // tier_id is missing
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Missing tier_id',
        message: 'tier_id is required when tier_name or tier_price is provided',
      });
    });

    it('should return 400 if tier_id is missing but tier_price is provided', async () => {
      const { req, res } = createMockApiContext('POST', {
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 120,
        tier_price: 120, // tier_price provided
        // tier_id is missing
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Missing tier_id',
        message: 'tier_id is required when tier_name or tier_price is provided',
      });
    });

    it('should return 400 for missing required fields (e.g., customer_id)', async () => {
      const { req, res } = createMockApiContext('POST', {
        // customer_id is missing
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 100,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Missing required fields',
        message: 'customer_id, service_id, assigned_artist_id, start_time, and end_time are required',
      });
    });

    it('should return 400 if customer_id is invalid', async () => {
      (mockQueryBuilder.single as jest.Mock).mockResolvedValueOnce({ data: null, error: { message: 'Customer not found', code: 'PGRST116' } }); // customer check fails

      const { req, res } = createMockApiContext('POST', {
        customer_id: 'invalid_cust_id',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 100,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ error: 'Invalid customer ID' });
    });

    it('should return 400 if service_id is invalid', async () => {
      const mockCustomer = { id: 'cust_123', first_name: 'John', last_name: 'Doe' };
      (mockQueryBuilder.single as jest.Mock)
        .mockResolvedValueOnce({ data: mockCustomer, error: null }) // customer check passes
        .mockResolvedValueOnce({ data: null, error: { message: 'Service not found', code: 'PGRST116' } }); // service check fails

      const { req, res } = createMockApiContext('POST', {
        customer_id: 'cust_123',
        service_id: 'invalid_serv_id',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 100,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ error: 'Invalid service ID' });
    });

    it('should return 400 if assigned_artist_id is invalid', async () => {
      const mockCustomer = { id: 'cust_123', first_name: 'John', last_name: 'Doe' };
      const mockService = { id: 'serv_456', name: 'Test Service', base_price: 100 };
      (mockQueryBuilder.single as jest.Mock)
        .mockResolvedValueOnce({ data: mockCustomer, error: null }) // customer check passes
        .mockResolvedValueOnce({ data: mockService, error: null })  // service check passes
        .mockResolvedValueOnce({ data: null, error: { message: 'Artist not found', code: 'PGRST116' } }); // artist check fails

      const { req, res } = createMockApiContext('POST', {
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'invalid_art_id',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 100,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ error: 'Invalid artist ID' });
    });

    it('should return 400 for booking conflict', async () => {
      const mockCustomer = { id: 'cust_123', first_name: 'John', last_name: 'Doe' };
      const mockService = { id: 'serv_456', name: 'Test Service', base_price: 100 };
      const mockArtist = { id: 'art_789', artist_name: 'Test Artist' };
      const conflictingBooking = { id: 'conf_book_1', start_time: '2024-08-15T09:30:00Z', end_time: '2024-08-15T10:30:00Z' };

      (mockQueryBuilder.single as jest.Mock)
        .mockResolvedValueOnce({ data: mockCustomer, error: null }) // customer check
        .mockResolvedValueOnce({ data: mockService, error: null })  // service check
        .mockResolvedValueOnce({ data: mockArtist, error: null });   // artist check

      (mockQueryBuilder.select as jest.Mock) // For conflict check
         // Mocking the 'or' call specifically if it's chained after select
        .mockImplementationOnce(() => ({
            eq: jest.fn().mockReturnThis(),
            neq: jest.fn().mockReturnThis(),
            or: jest.fn().mockResolvedValue({ data: [conflictingBooking], error: null }),
        }));


      const { req, res } = createMockApiContext('POST', {
        customer_id: 'cust_123',
        service_id: 'serv_456',
        assigned_artist_id: 'art_789',
        start_time: '2024-08-15T10:00:00.000Z',
        end_time: '2024-08-15T11:00:00.000Z',
        total_amount: 100,
      });

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Booking conflict',
        message: 'The selected artist already has a booking during this time slot',
      });
    });
  });
});
