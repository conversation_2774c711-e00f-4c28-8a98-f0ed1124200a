/* Booking Calendar Styles */

.calendarContainer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendarHeader {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.monthStats {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.statItem {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Status Legend */
.statusLegend {
  background: #f1f5f9;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.legendTitle {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.legendColor {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legendIcon {
  font-size: 0.8rem;
  width: 16px;
  text-align: center;
}

.legendLabel {
  color: #4b5563;
  font-weight: 500;
}

.monthNavigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navButton {
  background: #3b82f6;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.navButton:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.monthTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  min-width: 200px;
  text-align: center;
}

.todayButton {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.todayButton:hover {
  background: #059669;
  transform: translateY(-1px);
}

.calendar {
  padding: 1rem;
}

.dayHeaders {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1rem;
}

.dayHeader {
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: #64748b;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calendarGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.calendarDay {
  background: white;
  min-height: 120px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid transparent;
}

.calendarDay:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.calendarDay.today {
  background: #eff6ff;
  border: 2px solid #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.calendarDay.hasBookings {
  background: #fefce8;
  border-color: #fbbf24;
}

.calendarDay.hasBookings:hover {
  background: #fef3c7;
}

.calendarDay.otherMonth {
  background: #f8fafc;
  color: #94a3b8;
}

.calendarDay.otherMonth .dayNumber {
  color: #cbd5e1;
}

.dayHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.dayNumber {
  font-weight: 600;
  font-size: 0.9rem;
  color: #1e293b;
}

.dayStats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.bookingCount {
  background: #3b82f6;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.15rem 0.4rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

.dayRevenue {
  background: #10b981;
  color: white;
  font-size: 0.65rem;
  font-weight: 500;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  line-height: 1;
}

.bookingsContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.bookingItem {
  background: #f1f5f9;
  border-left: 3px solid #3b82f6;
  padding: 0.3rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bookingItem:hover {
  background: white;
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.1);
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.15rem;
}

.bookingTime {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.7rem;
}

.statusIcon {
  font-size: 0.7rem;
  font-weight: bold;
}

.bookingCustomer {
  color: #475569;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.7rem;
  margin-bottom: 0.1rem;
}

.bookingService {
  color: #64748b;
  font-size: 0.65rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.1rem;
}

.bookingAmount {
  color: #059669;
  font-size: 0.65rem;
  font-weight: 600;
}

.moreBookings {
  background: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  text-align: center;
  font-weight: 500;
  margin-top: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .calendarDay {
    min-height: 100px;
  }
  
  .bookingItem {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .bookingTime {
    font-size: 0.65rem;
  }
  
  .bookingService {
    font-size: 0.6rem;
  }
}

@media (max-width: 768px) {
  .calendarHeader {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .headerActions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .monthStats {
    justify-content: center;
  }

  .statItem {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .statusLegend {
    padding: 1rem;
    gap: 1rem;
    justify-content: center;
  }

  .legendItem {
    font-size: 0.8rem;
  }

  .monthTitle {
    font-size: 1.3rem;
    min-width: auto;
  }

  .calendar {
    padding: 0.5rem;
  }

  .calendarDay {
    min-height: 80px;
    padding: 0.25rem;
  }

  .dayHeader {
    margin-bottom: 0.25rem;
  }

  .dayNumber {
    font-size: 0.8rem;
  }

  .dayStats {
    gap: 0.15rem;
  }

  .bookingCount {
    font-size: 0.65rem;
    padding: 0.1rem 0.3rem;
    min-width: 16px;
  }

  .dayRevenue {
    font-size: 0.6rem;
    padding: 0.05rem 0.25rem;
  }

  .bookingItem {
    font-size: 0.65rem;
    padding: 0.2rem 0.3rem;
    min-height: 18px;
  }

  .bookingHeader {
    margin-bottom: 0.1rem;
  }

  .bookingTime {
    font-size: 0.6rem;
  }

  .statusIcon {
    font-size: 0.6rem;
  }

  .bookingCustomer {
    font-size: 0.6rem;
    margin-bottom: 0.05rem;
  }

  .bookingService {
    display: none; /* Hide service name on mobile to save space */
  }

  .bookingAmount {
    font-size: 0.6rem;
  }

  .moreBookings {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
}

@media (max-width: 480px) {
  .statusLegend {
    flex-direction: column;
    align-items: flex-start;
  }

  .monthStats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .calendarDay {
    min-height: 70px;
    padding: 0.2rem;
  }

  .dayStats {
    display: none; /* Hide stats on very small screens */
  }

  .bookingItem {
    padding: 0.15rem 0.25rem;
    min-height: 16px;
  }

  .bookingAmount {
    display: none; /* Hide amount on very small screens */
  }
}

@media (max-width: 480px) {
  .calendarGrid {
    gap: 0;
  }
  
  .calendarDay {
    min-height: 60px;
    padding: 0.2rem;
  }
  
  .dayNumber {
    font-size: 0.75rem;
  }
  
  .bookingItem {
    font-size: 0.6rem;
    padding: 0.1rem 0.2rem;
    min-height: 14px;
  }
  
  .bookingTime {
    font-size: 0.55rem;
  }
  
  .bookingCustomer {
    font-size: 0.55rem;
  }
}
