/* Service Form Styles (Create/Edit) */

.serviceFormContainer {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */ /* Removed */
  /* font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; */ /* Removed */
}

.header {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--admin-shadow-light); /* Changed */
  border-bottom: 1px solid var(--admin-border-light); /* Changed */
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--admin-gray); /* Changed */
  font-size: 0.875rem;
}

.breadcrumb a {
  color: var(--admin-primary); /* Changed */
  text-decoration: none;
  font-weight: 500;
}

.breadcrumb a:hover {
  color: var(--admin-primary-dark); /* Changed */
}

.breadcrumb span {
  color: var(--admin-gray); /* Changed to be consistent with other breadcrumb text */
}

.backButton {
  padding: 0.5rem 1rem;
  background: var(--admin-secondary); /* Changed */
  color: var(--admin-white); /* Changed */
  text-decoration: none;
  border-radius: var(--admin-radius-md); /* Changed */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-medium); /* Changed */
  background: var(--admin-dark); /* Darken secondary on hover */
}

.formContent {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.formContent h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--admin-darker); /* Changed */
  margin: 0 0 2rem 0;
  text-align: center;
  text-shadow: none; /* Changed */
}

.form {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  border-radius: var(--admin-radius-lg); /* Changed */
  padding: var(--admin-spacing-xl); /* Changed */
  box-shadow: var(--admin-shadow-light); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
}

.errorAlert {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 600; /* This can rely on global label style if it's 500 */
  color: var(--admin-darker); /* Changed */
  font-size: 0.875rem; /* This can rely on global label style if it's 0.9rem */
}

/* Specific input, select, textarea styles below are removed as they should be inherited from globals.css */
/* .formGroup input,
.formGroup select,
.formGroup textarea { ... } */

/* .formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus { ... } */

/* .inputError and .errorText will be replaced by global .input-error and .error-text */
/* .inputError { ... } */
/* .errorText { ... } */

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

/* .errorAlert removed, should use global .alert .alert-danger */

.visibilitySection {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--admin-bg-secondary); /* Changed */
  border-radius: var(--admin-radius-md); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
}

.visibilitySection h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-darker); /* Changed */
  margin: 0 0 1rem 0;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--admin-darker); /* Changed */
}

/* .checkboxLabel input[type="checkbox"] styling removed, rely on global */

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid var(--admin-border-light); /* Changed */
}

.submitButton {
  padding: 0.75rem 2rem;
  background: var(--admin-primary); /* Changed */
  color: var(--admin-white); /* Changed */
  border: none;
  border-radius: var(--admin-radius-md); /* Changed */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  background: var(--admin-primary-dark); /* Changed */
  box-shadow: var(--admin-shadow-medium); /* Changed */
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelButton {
  padding: 0.75rem 2rem;
  background: var(--admin-bg-secondary); /* Changed */
  color: var(--admin-gray); /* Changed */
  border: 1px solid var(--admin-border-medium); /* Changed */
  border-radius: var(--admin-radius-md); /* Changed */
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cancelButton:hover {
  background: var(--admin-bg-tertiary); /* Changed */
  border-color: var(--admin-gray); /* Changed */
}

/* Loading and Error States */
.loadingContainer, .errorContainer, .notFoundContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: var(--admin-darker); /* Changed */
  background: var(--admin-bg-secondary); /* Added background for consistency */
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--admin-primary-light-transparent); /* Changed */
  border-top: 4px solid var(--admin-primary); /* Changed */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h2, .notFoundContainer h2 {
  color: var(--admin-darker); /* Changed */
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .formContent {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .submitButton, .cancelButton {
    width: 100%;
    justify-content: center;
  }
}
