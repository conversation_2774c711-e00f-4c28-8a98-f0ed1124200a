import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../../hooks/useAuth';
import styles from '../../../styles/admin/CustomerAnalytics.module.css';

export default function CustomerAnalytics() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState([]);
  const [analytics, setAnalytics] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    newCustomersThisMonth: 0,
    averageBookingsPerCustomer: 0,
    topCustomers: [],
    customerGrowth: [],
    bookingTrends: []
  });
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('month'); // week, month, quarter, year

  useEffect(() => {
    if (!authLoading && user) {
      fetchCustomerAnalytics();
    }
  }, [user, authLoading, timeRange]);

  const fetchCustomerAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/customers/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customer analytics');
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (err) {
      console.error('Error fetching customer analytics:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading customer analytics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h2>Error Loading Analytics</h2>
        <p>{error}</p>
        <button onClick={fetchCustomerAnalytics} className={styles.retryButton}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Customer Analytics - Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Customer analytics and insights" />
      </Head>

      <div className={styles.analyticsContainer}>
        <header className={styles.header}>
          <div className={styles.headerContent}>
            <div className={styles.breadcrumb}>
              <Link href="/admin/customers">Customers</Link>
              <span> / </span>
              <span>Analytics</span>
            </div>
            <h1>Customer Analytics</h1>
            <p>Comprehensive insights into your customer base and behavior</p>
          </div>
          <div className={styles.headerActions}>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className={styles.timeRangeSelect}
            >
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 3 Months</option>
              <option value="year">Last 12 Months</option>
            </select>
            <Link href="/admin/customers" className={styles.backButton}>
              ← Back to Customers
            </Link>
          </div>
        </header>

        {/* Key Metrics Overview */}
        <section className={styles.metricsSection}>
          <h2>Key Metrics</h2>
          <div className={styles.metricsGrid}>
            <div className={styles.metricCard}>
              <div className={styles.metricIcon}>👥</div>
              <div className={styles.metricContent}>
                <div className={styles.metricValue}>{analytics.totalCustomers}</div>
                <div className={styles.metricLabel}>Total Customers</div>
              </div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricIcon}>✨</div>
              <div className={styles.metricContent}>
                <div className={styles.metricValue}>{analytics.activeCustomers}</div>
                <div className={styles.metricLabel}>Active Customers</div>
              </div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricIcon}>📈</div>
              <div className={styles.metricContent}>
                <div className={styles.metricValue}>{analytics.newCustomersThisMonth}</div>
                <div className={styles.metricLabel}>New This Month</div>
              </div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricIcon}>📊</div>
              <div className={styles.metricContent}>
                <div className={styles.metricValue}>{analytics.averageBookingsPerCustomer}</div>
                <div className={styles.metricLabel}>Avg Bookings/Customer</div>
              </div>
            </div>
          </div>
        </section>

        {/* Customer Growth Chart */}
        <section className={styles.chartSection}>
          <h2>Customer Growth</h2>
          <div className={styles.chartContainer}>
            <div className={styles.chartPlaceholder}>
              <p>Customer growth chart will be implemented here</p>
              <p>Showing growth over {timeRange}</p>
            </div>
          </div>
        </section>

        {/* Top Customers */}
        <section className={styles.topCustomersSection}>
          <h2>Top Customers</h2>
          <div className={styles.topCustomersList}>
            {analytics.topCustomers.length === 0 ? (
              <div className={styles.emptyState}>
                <p>No customer data available</p>
              </div>
            ) : (
              analytics.topCustomers.map((customer, index) => (
                <div key={customer.id} className={styles.topCustomerCard}>
                  <div className={styles.customerRank}>#{index + 1}</div>
                  <div className={styles.customerInfo}>
                    <div className={styles.customerName}>
                      {customer.first_name} {customer.last_name}
                    </div>
                    <div className={styles.customerEmail}>{customer.email}</div>
                  </div>
                  <div className={styles.customerStats}>
                    <div className={styles.statItem}>
                      <span className={styles.statValue}>{customer.total_bookings}</span>
                      <span className={styles.statLabel}>Bookings</span>
                    </div>
                    <div className={styles.statItem}>
                      <span className={styles.statValue}>${customer.total_spent}</span>
                      <span className={styles.statLabel}>Total Spent</span>
                    </div>
                  </div>
                  <Link href={`/admin/customers/${customer.id}`} className={styles.viewCustomerBtn}>
                    View
                  </Link>
                </div>
              ))
            )}
          </div>
        </section>

        {/* Customer Segments */}
        <section className={styles.segmentsSection}>
          <h2>Customer Segments</h2>
          <div className={styles.segmentsGrid}>
            <div className={styles.segmentCard}>
              <h3>New Customers</h3>
              <div className={styles.segmentValue}>{analytics.newCustomers || 0}</div>
              <p>Customers with 1-2 bookings</p>
            </div>
            <div className={styles.segmentCard}>
              <h3>Regular Customers</h3>
              <div className={styles.segmentValue}>{analytics.regularCustomers || 0}</div>
              <p>Customers with 3-10 bookings</p>
            </div>
            <div className={styles.segmentCard}>
              <h3>VIP Customers</h3>
              <div className={styles.segmentValue}>{analytics.vipCustomers || 0}</div>
              <p>Customers with 10+ bookings</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
