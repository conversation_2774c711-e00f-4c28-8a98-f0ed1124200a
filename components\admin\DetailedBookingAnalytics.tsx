import { useState, useEffect } from 'react';
import styles from '../../styles/admin/DetailedBookingAnalytics.module.css';

interface Booking {
  id: string;
  customer_name: string;
  service_name: string;
  artist_name: string;
  start_time: string;
  end_time: string;
  status: string;
  total_amount: number;
  created_at: string;
}

interface DetailedBookingAnalyticsProps {
  bookings: Booking[];
  dateRange?: string;
}

interface DetailedAnalyticsData {
  totalBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  statusBreakdown: Record<string, number>;
  topServices: Array<{ name: string; count: number; revenue: number }>;
  topArtists: Array<{ name: string; count: number; revenue: number }>;
  monthlyTrend: Array<{ month: string; bookings: number; revenue: number }>;
  cancellationRate: number;
  completionRate: number;
}

export default function DetailedBookingAnalytics({ bookings, dateRange = '30d' }: DetailedBookingAnalyticsProps) {
  const [analytics, setAnalytics] = useState<DetailedAnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>(dateRange as any || '30d');

  useEffect(() => {
    calculateDetailedAnalytics();
  }, [bookings, timeRange]);

  const calculateDetailedAnalytics = () => {
    if (!bookings || bookings.length === 0) {
      setAnalytics(null);
      return;
    }

    const now = new Date();
    const cutoffDate = new Date();
    
    switch (timeRange) {
      case '7d':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    // Filter bookings by time range
    const filteredBookings = bookings.filter(booking => {
      const bookingDate = new Date(booking.start_time || booking.created_at);
      return bookingDate >= cutoffDate;
    });

    // Calculate basic metrics
    const totalBookings = filteredBookings.length;
    const totalRevenue = filteredBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;

    // Status breakdown
    const statusBreakdown: Record<string, number> = {};
    filteredBookings.forEach(booking => {
      const status = booking.status || 'unknown';
      statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
    });

    // Calculate rates
    const completedBookings = statusBreakdown['completed'] || 0;
    const cancelledBookings = statusBreakdown['cancelled'] || 0;
    const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;
    const completionRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;

    // Top services
    const serviceStats: Record<string, { count: number; revenue: number }> = {};
    filteredBookings.forEach(booking => {
      const service = booking.service_name || 'Unknown Service';
      if (!serviceStats[service]) {
        serviceStats[service] = { count: 0, revenue: 0 };
      }
      serviceStats[service].count++;
      serviceStats[service].revenue += booking.total_amount || 0;
    });

    const topServices = Object.entries(serviceStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Top artists
    const artistStats: Record<string, { count: number; revenue: number }> = {};
    filteredBookings.forEach(booking => {
      const artist = booking.artist_name || 'Unassigned';
      if (!artistStats[artist]) {
        artistStats[artist] = { count: 0, revenue: 0 };
      }
      artistStats[artist].count++;
      artistStats[artist].revenue += booking.total_amount || 0;
    });

    const topArtists = Object.entries(artistStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Monthly trend (last 12 months for detailed view)
    const monthlyTrend = [];
    const monthsToShow = timeRange === '1y' ? 12 : 6;
    for (let i = monthsToShow - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      const monthBookings = bookings.filter(booking => {
        const bookingDate = new Date(booking.start_time || booking.created_at);
        return bookingDate >= monthStart && bookingDate <= monthEnd;
      });

      monthlyTrend.push({
        month: date.toLocaleDateString('en-AU', { month: 'short', year: 'numeric' }),
        bookings: monthBookings.length,
        revenue: monthBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0)
      });
    }

    setAnalytics({
      totalBookings,
      totalRevenue,
      averageBookingValue,
      statusBreakdown,
      topServices,
      topArtists,
      monthlyTrend,
      cancellationRate,
      completionRate
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'cancelled': return '#dc3545';
      case 'completed': return '#17a2b8';
      case 'no_show': return '#6c757d';
      default: return '#6c757d';
    }
  };

  if (!analytics) {
    return (
      <div className={styles.analyticsContainer}>
        <div className={styles.emptyState}>
          <p>No booking data available for detailed analysis</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.analyticsContainer}>
      <div className={styles.analyticsHeader}>
        <h3>Detailed Booking Analytics</h3>
        <div className={styles.timeRangeSelector}>
          <button
            className={timeRange === '7d' ? styles.active : ''}
            onClick={() => setTimeRange('7d')}
          >
            7 Days
          </button>
          <button
            className={timeRange === '30d' ? styles.active : ''}
            onClick={() => setTimeRange('30d')}
          >
            30 Days
          </button>
          <button
            className={timeRange === '90d' ? styles.active : ''}
            onClick={() => setTimeRange('90d')}
          >
            90 Days
          </button>
          <button
            className={timeRange === '1y' ? styles.active : ''}
            onClick={() => setTimeRange('1y')}
          >
            1 Year
          </button>
        </div>
      </div>

      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.totalBookings}</div>
          <div className={styles.metricLabel}>Total Bookings</div>
        </div>
        
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{formatCurrency(analytics.totalRevenue)}</div>
          <div className={styles.metricLabel}>Total Revenue</div>
        </div>
        
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{formatCurrency(analytics.averageBookingValue)}</div>
          <div className={styles.metricLabel}>Avg Booking Value</div>
        </div>
        
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.completionRate.toFixed(1)}%</div>
          <div className={styles.metricLabel}>Completion Rate</div>
        </div>
      </div>

      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h4>Booking Status Breakdown</h4>
          <div className={styles.statusChart}>
            {Object.entries(analytics.statusBreakdown).map(([status, count]) => (
              <div key={status} className={styles.statusItem}>
                <div
                  className={styles.statusIndicator}
                  style={{ backgroundColor: getStatusColor(status) }}
                />
                <span className={styles.statusLabel}>{status}</span>
                <span className={styles.statusCount}>{count}</span>
                <span className={styles.statusPercentage}>
                  ({((count / analytics.totalBookings) * 100).toFixed(1)}%)
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h4>Top Services by Revenue</h4>
          <div className={styles.topList}>
            {analytics.topServices.map((service, index) => (
              <div key={service.name} className={styles.topItem}>
                <div className={styles.topRank}>#{index + 1}</div>
                <div className={styles.topInfo}>
                  <div className={styles.topName}>{service.name}</div>
                  <div className={styles.topStats}>
                    {service.count} bookings • {formatCurrency(service.revenue)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h4>Top Artists by Revenue</h4>
          <div className={styles.topList}>
            {analytics.topArtists.map((artist, index) => (
              <div key={artist.name} className={styles.topItem}>
                <div className={styles.topRank}>#{index + 1}</div>
                <div className={styles.topInfo}>
                  <div className={styles.topName}>{artist.name}</div>
                  <div className={styles.topStats}>
                    {artist.count} bookings • {formatCurrency(artist.revenue)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h4>Monthly Trend</h4>
          <div className={styles.trendChart}>
            {analytics.monthlyTrend.map((month, index) => (
              <div key={index} className={styles.trendItem}>
                <div className={styles.trendMonth}>{month.month}</div>
                <div className={styles.trendBookings}>{month.bookings} bookings</div>
                <div className={styles.trendRevenue}>{formatCurrency(month.revenue)}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
