/* Customer Analytics Page Styles - Consistent Design System */

.analyticsContainer {
  min-height: 100vh;
  background: var(--admin-bg-secondary);
  font-family: inherit;
}

.header {
  background: var(--admin-bg-primary);
  padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px var(--admin-shadow-light);
  border-bottom: 1px solid var(--admin-border-light);
}

.headerContent h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--admin-darker);
  margin: 0 0 var(--admin-spacing-xs) 0;
}

.headerContent p {
  margin: 0;
  color: var(--admin-gray);
  font-size: 0.95rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
  margin-bottom: var(--admin-spacing-sm);
  font-size: 0.875rem;
}

.breadcrumb a {
  color: var(--admin-primary);
  text-decoration: none;
  font-weight: 500;
}

.breadcrumb a:hover {
  color: var(--admin-primary-dark);
}

.breadcrumb span {
  color: var(--admin-gray);
}

.headerActions {
  display: flex;
  gap: var(--admin-spacing-sm);
  align-items: center;
}

.timeRangeSelect {
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  background: var(--admin-bg-primary);
  color: var(--admin-darker);
  font-size: 0.875rem;
  cursor: pointer;
}

.backButton {
  background: var(--admin-bg-tertiary);
  color: var(--admin-gray);
  border: 1px solid var(--admin-border-light);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  text-decoration: none;
  font-size: 0.875rem;
}

.backButton:hover {
  background: var(--admin-bg-secondary);
  border-color: var(--admin-border-medium);
}

/* Metrics Section */
.metricsSection {
  padding: var(--admin-spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.metricsSection h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-darker);
  margin: 0 0 var(--admin-spacing-lg) 0;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-spacing-lg);
}

.metricCard {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  transition: all var(--admin-transition-normal);
}

.metricCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--admin-shadow-medium);
}

.metricIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--admin-bg-tertiary);
  border-radius: var(--admin-radius-lg);
}

.metricContent {
  flex: 1;
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--admin-primary);
  margin-bottom: var(--admin-spacing-xs);
}

.metricLabel {
  font-size: 0.875rem;
  color: var(--admin-gray);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chartCard {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.chartCard h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #667eea;
}

.barChart {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 200px;
  gap: 0.5rem;
  padding: 1rem 0;
}

.barContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
  max-width: 40px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: auto;
}

.bar:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: scaleY(1.05);
}

.barLabel {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-align: center;
}

.barValue {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.topCustomersList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topCustomerItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.topCustomerItem:hover {
  background: #f1f5f9;
  transform: translateX(4px);
}

.customerRank {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.customerInfo {
  flex: 1;
}

.customerName {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.customerEmail {
  color: #64748b;
  font-size: 0.875rem;
}

.customerBookings {
  text-align: right;
}

.bookingCount {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.bookingLabel {
  color: #64748b;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.emptyState {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.insightCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.insightCard h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
}

.insightContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.engagementStat, .trendStat, .retentionStat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.percentage, .trendValue, .retentionValue {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

.engagementLabel, .trendLabel, .retentionLabel {
  color: #64748b;
  font-size: 0.875rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .metricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .analyticsContainer {
    padding: 1.5rem;
  }
  
  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .metricCard {
    padding: 1.5rem;
  }
  
  .metricValue {
    font-size: 2rem;
  }
  
  .chartCard {
    padding: 1.5rem;
  }
  
  .barChart {
    height: 150px;
  }
  
  .insightsGrid {
    grid-template-columns: 1fr;
  }
  
  .topCustomerItem {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .customerBookings {
    text-align: center;
  }
}
