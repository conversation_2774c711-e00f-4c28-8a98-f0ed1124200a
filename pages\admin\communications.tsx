import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/Communications.module.css';

interface Communication {
  id: string;
  customer_id: string;
  booking_id?: string;
  template_id?: string;
  communication_type: 'email' | 'sms';
  recipient: string;
  subject?: string;
  status: 'pending' | 'sent' | 'failed' | 'delivered' | 'opened';
  sent_at?: string;
  delivered_at?: string;
  opened_at?: string;
  error_message?: string;
  created_at: string;
  customers: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  bookings?: {
    id: string;
    start_time: string;
    services: {
      name: string;
    };
  };
  email_templates?: {
    id: string;
    name: string;
    type: string;
  };
}

const COMMUNICATION_TYPES = [
  { value: 'all', label: 'All Types' },
  { value: 'email', label: 'Email' },
  { value: 'sms', label: 'SMS' }
];

const COMMUNICATION_STATUSES = [
  { value: 'all', label: 'All Statuses' },
  { value: 'pending', label: 'Pending' },
  { value: 'sent', label: 'Sent' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'opened', label: 'Opened' },
  { value: 'failed', label: 'Failed' }
];

export default function CommunicationsPage() {
  const { user } = useAuth();
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [limit] = useState(25);

  useEffect(() => {
    loadCommunications();
  }, [selectedType, selectedStatus, currentPage]);

  const loadCommunications = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedType !== 'all') params.append('type', selectedType);
      if (selectedStatus !== 'all') params.append('status', selectedStatus);
      params.append('limit', limit.toString());
      params.append('offset', ((currentPage - 1) * limit).toString());

      const response = await fetch(`/api/admin/communications?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCommunications(data.communications || []);
        setTotalCount(data.total || 0);
      } else {
        setError('Failed to load communications');
      }
    } catch (error) {
      console.error('Error loading communications:', error);
      setError('Failed to load communications');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return '#10b981';
      case 'delivered': return '#059669';
      case 'opened': return '#047857';
      case 'failed': return '#ef4444';
      case 'pending': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'email' ? '📧' : '📱';
  };

  const totalPages = Math.ceil(totalCount / limit);

  if (loading && communications.length === 0) {
    return (
      <>
        <Head>
          <title>Loading Communications - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading communications...</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Customer Communications | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="View and manage customer communications history" />
      </Head>

      <div className={styles.communicationsContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Customer Communications</h1>
            <p className={styles.subtitle}>View and track all customer communications</p>
          </div>
          <div className={styles.headerStats}>
            <div className={styles.statCard}>
              <span className={styles.statNumber}>{totalCount}</span>
              <span className={styles.statLabel}>Total Communications</span>
            </div>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        <div className={styles.filters}>
          <div className={styles.filterGroup}>
            <label htmlFor="typeFilter">Type:</label>
            <select
              id="typeFilter"
              value={selectedType}
              onChange={(e) => {
                setSelectedType(e.target.value);
                setCurrentPage(1);
              }}
              className={styles.filterSelect}
            >
              {COMMUNICATION_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.filterGroup}>
            <label htmlFor="statusFilter">Status:</label>
            <select
              id="statusFilter"
              value={selectedStatus}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
              className={styles.filterSelect}
            >
              {COMMUNICATION_STATUSES.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className={styles.communicationsTable}>
          {communications.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No communications found</h3>
              <p>No customer communications match your current filters.</p>
            </div>
          ) : (
            <div className={styles.tableContainer}>
              <table className={styles.table}>
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Customer</th>
                    <th>Recipient</th>
                    <th>Subject/Template</th>
                    <th>Status</th>
                    <th>Sent</th>
                    <th>Delivered</th>
                  </tr>
                </thead>
                <tbody>
                  {communications.map((comm) => (
                    <tr key={comm.id} className={styles.tableRow}>
                      <td>
                        <div className={styles.typeCell}>
                          <span className={styles.typeIcon}>{getTypeIcon(comm.communication_type)}</span>
                          <span className={styles.typeText}>{comm.communication_type.toUpperCase()}</span>
                        </div>
                      </td>
                      <td>
                        <div className={styles.customerCell}>
                          <span className={styles.customerName}>
                            {comm.customers.first_name} {comm.customers.last_name}
                          </span>
                          {comm.bookings && (
                            <span className={styles.bookingInfo}>
                              {comm.bookings.services.name} - {formatDate(comm.bookings.start_time)}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className={styles.recipientCell}>{comm.recipient}</td>
                      <td>
                        <div className={styles.subjectCell}>
                          {comm.subject && <div className={styles.subject}>{comm.subject}</div>}
                          {comm.email_templates && (
                            <div className={styles.templateInfo}>
                              {comm.email_templates.name} ({comm.email_templates.type})
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <span 
                          className={styles.statusBadge}
                          style={{ backgroundColor: getStatusColor(comm.status) }}
                        >
                          {comm.status.toUpperCase()}
                        </span>
                        {comm.error_message && (
                          <div className={styles.errorInfo} title={comm.error_message}>
                            ⚠️ Error
                          </div>
                        )}
                      </td>
                      <td className={styles.dateCell}>
                        {comm.sent_at ? formatDate(comm.sent_at) : '-'}
                      </td>
                      <td className={styles.dateCell}>
                        {comm.delivered_at ? formatDate(comm.delivered_at) : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className={styles.paginationBtn}
            >
              Previous
            </button>
            <span className={styles.paginationInfo}>
              Page {currentPage} of {totalPages} ({totalCount} total)
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className={styles.paginationBtn}
            >
              Next
            </button>
          </div>
        )}
      </div>
    </>
  );
}
