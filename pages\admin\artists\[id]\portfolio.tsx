import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import PortfolioManager from '../../../../components/admin/PortfolioManager';
import { useAuth } from '../../../../hooks/useAuth';
import styles from '../../../../styles/admin/Portfolio.module.css';

interface Artist {
  id: string;
  name: string;
  email: string;
  specializations?: string[];
  bio?: string;
  rating?: number;
  total_bookings?: number;
}

interface PortfolioStats {
  totalItems: number;
  featuredItems: number;
  publicItems: number;
  categories: string[];
  lastUpdated: string | null;
}

export default function IndividualArtistPortfolioPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { id: artistId } = router.query;
  const [artist, setArtist] = useState<Artist | null>(null);
  const [stats, setStats] = useState<PortfolioStats>({
    totalItems: 0,
    featuredItems: 0,
    publicItems: 0,
    categories: [],
    lastUpdated: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && artistId) {
      loadArtistData();
    }
  }, [user, artistId]);

  const loadArtistData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('adminToken');
      
      // Load artist information
      const artistResponse = await fetch(`/api/admin/artists/${artistId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!artistResponse.ok) {
        throw new Error('Artist not found');
      }

      const artistData = await artistResponse.json();
      setArtist(artistData.artist);

      // Load portfolio statistics
      const portfolioResponse = await fetch(`/api/admin/artists/${artistId}/portfolio`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (portfolioResponse.ok) {
        const portfolioData = await portfolioResponse.json();
        setStats(portfolioData.stats || {
          totalItems: 0,
          featuredItems: 0,
          publicItems: 0,
          categories: [],
          lastUpdated: null
        });
      }

    } catch (err) {
      console.error('Error loading artist data:', err);
      setError('Failed to load artist information');
    } finally {
      setLoading(false);
    }
  };

  const handleItemAdded = () => {
    loadArtistData();
  };

  const handleItemUpdated = () => {
    loadArtistData();
  };

  const handleItemDeleted = () => {
    loadArtistData();
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Portfolio - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading artist portfolio...</p>
        </div>
      </>
    );
  }

  if (!user) {
    router.push('/admin/login');
    return null;
  }

  // Check permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <>
        <Head>
          <title>Access Denied - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.accessDenied}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access portfolio management.</p>
        </div>
      </>
    );
  }

  if (error || !artist) {
    return (
      <>
        <Head>
          <title>Error - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.error}>
          <h2>Artist Not Found</h2>
          <p>{error || 'The specified artist could not be found.'}</p>
          <Link href="/admin/artists/portfolio" className={styles.backButton}>
            ← Back to Portfolio Management
          </Link>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{artist.name} - Portfolio | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Manage ${artist.name}'s portfolio and work samples`} />
      </Head>

      <div className={styles.portfolioPage}>
        {/* Breadcrumb Navigation */}
        <div className={styles.breadcrumb}>
          <Link href="/admin/artists">Artists</Link>
          <span>›</span>
          <Link href="/admin/artists/portfolio">Portfolio Management</Link>
          <span>›</span>
          <span>{artist.name}</span>
        </div>

        {/* Artist Header */}
        <div className={styles.artistHeader}>
          <div className={styles.artistInfo}>
            <h1>{artist.name}'s Portfolio</h1>
            <div className={styles.artistDetails}>
              <p className={styles.email}>{artist.email}</p>
              {artist.specializations && artist.specializations.length > 0 && (
                <div className={styles.specializations}>
                  <strong>Specializations:</strong>
                  <div className={styles.specializationTags}>
                    {artist.specializations.map(spec => (
                      <span key={spec} className={styles.specializationTag}>
                        {spec.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {artist.bio && (
                <p className={styles.bio}>{artist.bio}</p>
              )}
            </div>
          </div>
          
          <div className={styles.artistStats}>
            {artist.rating && (
              <div className={styles.statItem}>
                <span className={styles.statValue}>⭐ {artist.rating.toFixed(1)}</span>
                <span className={styles.statLabel}>Rating</span>
              </div>
            )}
            {artist.total_bookings && (
              <div className={styles.statItem}>
                <span className={styles.statValue}>{artist.total_bookings}</span>
                <span className={styles.statLabel}>Total Bookings</span>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            <p>{error}</p>
            <button onClick={() => setError(null)}>×</button>
          </div>
        )}

        {/* Portfolio Statistics */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>🎨</div>
            <div className={styles.statContent}>
              <h3>{stats.totalItems}</h3>
              <p>Portfolio Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>⭐</div>
            <div className={styles.statContent}>
              <h3>{stats.featuredItems}</h3>
              <p>Featured Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👁️</div>
            <div className={styles.statContent}>
              <h3>{stats.publicItems}</h3>
              <p>Public Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>📂</div>
            <div className={styles.statContent}>
              <h3>{stats.categories.length}</h3>
              <p>Categories</p>
            </div>
          </div>
        </div>

        {/* Categories Overview */}
        {stats.categories.length > 0 && (
          <div className={styles.categoriesOverview}>
            <h3>Portfolio Categories</h3>
            <div className={styles.categoryTags}>
              {stats.categories.map(category => (
                <span key={category} className={styles.categoryTag}>
                  {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Portfolio Manager Component */}
        <PortfolioManager
          artistId={artistId as string}
          onItemAdded={handleItemAdded}
          onItemUpdated={handleItemUpdated}
          onItemDeleted={handleItemDeleted}
        />
      </div>
    </>
  );
}
