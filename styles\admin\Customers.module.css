/* Customer Management Styles */

.customersContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerContent h1 {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
}

.headerContent p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.analyticsBtn, .exportBtn {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 0.9rem;
}

.analyticsBtn:hover, .exportBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.newCustomerBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.newCustomerBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.backButton {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.controlsPanel {
  background: var(--admin-bg-primary);
  padding: var(--admin-spacing-md) var(--admin-spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--admin-border-light);
  gap: var(--admin-spacing-md);
}

.searchContainer {
  flex: 1;
  min-width: 300px;
}

.filtersContainer {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filterSelect, .sortSelect {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.viewControls {
  display: flex;
  gap: 0.25rem;
  background: #f1f5f9;
  padding: 0.25rem;
  border-radius: 8px;
}

.viewBtn {
  background: transparent;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s ease;
}

.viewBtn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.resultsInfo {
  background: rgba(255, 255, 255, 0.9);
  padding: 0.75rem 2rem;
  color: #64748b;
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.searchSection {
  flex: 1;
  min-width: 300px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.sortSection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sortSection label {
  color: #475569;
  font-weight: 500;
  font-size: 0.875rem;
}

.sortSelect {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.sortOrderBtn {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  color: #475569;
  transition: all 0.3s ease;
}

.sortOrderBtn:hover {
  background: #e2e8f0;
}

.customersContent {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.customersHeader {
  margin-bottom: 2rem;
}

.statsCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.statCard h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statCard .statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.emptyState {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  color: #64748b;
  font-size: 1.1rem;
}

.customersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.customerCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.customerCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.customerMeta {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.customerDate {
  color: #64748b;
  font-size: 0.875rem;
}

.emptyState {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 4rem 2rem;
  border-radius: 12px;
  text-align: center;
  color: #64748b;
  margin: 2rem 0;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
}

.emptyState p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.emptyActionBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.emptyActionBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.customerHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.customerInfo h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.customerInfo p {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusVip {
  background: #fef3c7;
  color: #92400e;
}

.statusActive {
  background: #d1fae5;
  color: #065f46;
}

.statusNew {
  background: #dbeafe;
  color: #1e40af;
}

.statusInactive {
  background: #fee2e2;
  color: #991b1b;
}

.statusDefault {
  background: #f1f5f9;
  color: #475569;
}

.customerStats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statLabel {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.statItem .statValue {
  color: #1e293b;
  font-weight: 600;
  font-size: 0.875rem;
}

.customerNotes {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #667eea;
  font-size: 0.875rem;
}

.customerNotes strong {
  color: #475569;
}

.customerActions {
  display: flex;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.viewBtn, .bookBtn {
  flex: 1;
  padding: 0.75rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.viewBtn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.bookBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.viewBtn:hover, .bookBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Table View Styles */
.customersTable {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.customersTable table {
  width: 100%;
  border-collapse: collapse;
}

.customersTable th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
}

.customersTable td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: #64748b;
}

.customersTable tr:hover {
  background: #f8fafc;
}

.customerName {
  font-weight: 600;
  color: #1e293b;
}

.bookingBadge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.tableActions {
  display: flex;
  gap: 0.5rem;
}

.tableActions .viewBtn, .tableActions .editBtn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.tableActions .viewBtn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.tableActions .editBtn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
}

.paginationBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paginationBtn:disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

.paginationBtn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.paginationInfo {
  color: #64748b;
  font-weight: 500;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 1024px) {
  .customersGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  }
}

@media (max-width: 768px) {
  .header {
    padding: var(--admin-spacing-md);
    flex-direction: column;
    gap: var(--admin-spacing-md);
  }

  .headerActions {
    width: 100%;
    justify-content: space-between;
  }

  .quickStatsSection {
    padding: var(--admin-spacing-md);
  }

  .quickStats {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-md);
  }

  .controlsPanel {
    padding: var(--admin-spacing-md);
    flex-direction: column;
    gap: var(--admin-spacing-md);
  }

  .searchSection {
    max-width: none;
  }

  .quickFilters {
    width: 100%;
    justify-content: center;
  }

  .customersGrid {
    grid-template-columns: 1fr;
    padding: var(--admin-spacing-md);
    gap: var(--admin-spacing-md);
  }

  .customerHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-spacing-sm);
  }

  .customerInfo {
    width: 100%;
  }

  .customerMeta {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .customerActions {
    flex-direction: column;
  }

  .pagination {
    padding: var(--admin-spacing-md);
  }

  .customersTable {
    margin: var(--admin-spacing-md);
  }

  .tableActions {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .tableActions .viewBtn,
  .tableActions .bookBtn,
  .tableActions .editBtn {
    padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: var(--admin-spacing-sm);
  }

  .headerContent h1 {
    font-size: 1.5rem;
  }

  .headerActions {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .quickStatsSection {
    padding: var(--admin-spacing-sm);
  }

  .controlsPanel {
    padding: var(--admin-spacing-sm);
  }

  .customersGrid {
    padding: var(--admin-spacing-sm);
  }

  .customerCard {
    padding: var(--admin-spacing-md);
  }

  .customerAvatar {
    width: 36px;
    height: 36px;
    font-size: 0.8rem;
  }

  .customerActions {
    gap: var(--admin-spacing-xs);
  }

  .viewButton, .bookButton, .editButton {
    padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
    font-size: 0.8rem;
  }
}
