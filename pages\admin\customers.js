import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import CustomerModal from '../../components/admin/customers/CustomerModal';
import CustomerAnalytics from '../../components/admin/customers/CustomerAnalytics';
import {
  getCustomerPriority,
  getPriorityInfo,
  getCustomerStatusInfo,
  getCustomerInitials,
  getCustomerIdDisplay,
  getLastBookingDate
} from '../../utils/customerHelpers';
import styles from '../../styles/admin/Customers.module.css';

export default function CustomersManagement() {
  const { user, loading: authLoading } = useAuth();
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filterBy, setFilterBy] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'table'
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch('/api/admin/customers', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load customers');
      }

      const data = await response.json();
      setCustomers(data.customers || []);
      setFilteredCustomers(data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
      setCustomers([]);
      setFilteredCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadCustomers();
    }
  }, [user, authLoading]);

  useEffect(() => {
    let filtered = customers;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(customer => {
        const fullName = `${customer.first_name || ''} ${customer.last_name || ''}`.toLowerCase();
        const email = (customer.email || '').toLowerCase();
        const phone = (customer.phone || '').toLowerCase();
        const search = searchTerm.toLowerCase();

        return fullName.includes(search) ||
               email.includes(search) ||
               phone.includes(search);
      });
    }

    // Apply status filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(customer => {
        switch (filterBy) {
          case 'recent':
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            return new Date(customer.created_at) > oneWeekAgo;
          case 'active':
            return customer.total_bookings > 0;
          case 'inactive':
            return customer.total_bookings === 0;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = `${a.first_name || ''} ${a.last_name || ''}`.toLowerCase();
          bValue = `${b.first_name || ''} ${b.last_name || ''}`.toLowerCase();
          break;
        case 'email':
          aValue = (a.email || '').toLowerCase();
          bValue = (b.email || '').toLowerCase();
          break;
        case 'bookings':
          aValue = a.total_bookings || 0;
          bValue = b.total_bookings || 0;
          break;
        case 'created_at':
        default:
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCustomers(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [customers, searchTerm, sortBy, sortOrder, filterBy]);

  // Pagination logic
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCustomers = filteredCustomers.slice(startIndex, endIndex);

  const handleCustomerClick = (customer) => {
    setSelectedCustomer(customer);
    setShowModal(true);
  };

  const handleCustomerUpdate = (updatedCustomer) => {
    setCustomers(prev => prev.map(c =>
      c.id === updatedCustomer.id ? updatedCustomer : c
    ));
    setShowModal(false);
    setSelectedCustomer(null);
  };

  const handleCustomerDelete = (customerId) => {
    setCustomers(prev => prev.filter(c => c.id !== customerId));
    setShowModal(false);
    setSelectedCustomer(null);
  };

  const exportCustomers = () => {
    const csvContent = [
      ['Name', 'Email', 'Phone', 'Total Bookings', 'Created Date'],
      ...filteredCustomers.map(customer => [
        `${customer.first_name || ''} ${customer.last_name || ''}`,
        customer.email || '',
        customer.phone || '',
        customer.total_bookings || 0,
        new Date(customer.created_at).toLocaleDateString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `customers-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Customers - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading customers...</p>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Customers Management - Ocean Soul Sparkles Admin</title>
      </Head>

      <div className={styles.customersContainer}>
        <header className={styles.header}>
          <div className={styles.headerContent}>
            <h1>Customer Management</h1>
            <p>Manage your customer database and view customer analytics</p>
          </div>
          <div className={styles.headerActions}>
            <button
              onClick={() => setShowAnalytics(!showAnalytics)}
              className={styles.analyticsBtn}
            >
              📊 Analytics
            </button>
            <button
              onClick={exportCustomers}
              className={styles.exportBtn}
            >
              📥 Export CSV
            </button>
            <Link href="/admin/customers/new" className={styles.newCustomerBtn}>
              ➕ New Customer
            </Link>
          </div>
        </header>

        {showAnalytics && (
          <CustomerAnalytics customers={customers} />
        )}

        <div className={styles.controlsPanel}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filtersContainer}>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Customers</option>
              <option value="recent">Recent (Last 7 days)</option>
              <option value="active">Active (Has bookings)</option>
              <option value="inactive">Inactive (No bookings)</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="created_at">Sort by Date Added</option>
              <option value="name">Sort by Name</option>
              <option value="email">Sort by Email</option>
              <option value="bookings">Sort by Bookings</option>
            </select>

            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className={styles.sortOrderBtn}
              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>

          <div className={styles.viewControls}>
            <button
              onClick={() => setViewMode('grid')}
              className={`${styles.viewBtn} ${viewMode === 'grid' ? styles.active : ''}`}
            >
              ⊞ Grid
            </button>
            <button
              onClick={() => setViewMode('table')}
              className={`${styles.viewBtn} ${viewMode === 'table' ? styles.active : ''}`}
            >
              ☰ Table
            </button>
          </div>
        </div>

        <div className={styles.resultsInfo}>
          <span>
            Showing {startIndex + 1}-{Math.min(endIndex, filteredCustomers.length)} of {filteredCustomers.length} customers
          </span>
        </div>

        {currentCustomers.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>👥</div>
            <h3>No customers found</h3>
            <p>
              {searchTerm || filterBy !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first customer'
              }
            </p>
            {!searchTerm && filterBy === 'all' && (
              <Link href="/admin/customers/new" className={styles.emptyActionBtn}>
                ➕ Add First Customer
              </Link>
            )}
          </div>
        ) : viewMode === 'grid' ? (
          <div className={styles.customersGrid}>
            {currentCustomers.map(customer => (
              <div
                key={customer.id}
                className={styles.customerCard}
              >
                <div className={styles.customerHeader}>
                  <div className={styles.customerInfo}>
                    <div className={styles.customerAvatar}>
                      {getCustomerInitials(customer)}
                    </div>
                    <div className={styles.customerDetails}>
                      <div className={styles.customerName}>
                        {customer.first_name} {customer.last_name}
                      </div>
                      <div className={styles.customerContact}>
                        {customer.email}
                      </div>
                    </div>
                  </div>
                  <div className={styles.customerStatus}>
                    <span className={`${styles.statusBadge} ${
                      getCustomerStatusInfo(customer).className
                    }`}>
                      {getCustomerStatusInfo(customer).label}
                    </span>
                    {(() => {
                      const priority = getCustomerPriority(customer);
                      const priorityInfo = getPriorityInfo(priority);
                      return (
                        <span
                          className={styles.priorityBadgeCard}
                          style={{ backgroundColor: priorityInfo.color }}
                          title={`Priority: ${priorityInfo.label}`}
                        >
                          {priorityInfo.icon}
                        </span>
                      );
                    })()}
                  </div>
                </div>

                <div className={styles.customerMeta}>
                  <div className={styles.metaItem}>
                    <span className={styles.metaIcon}>📅</span>
                    <span className={styles.metaText}>
                      {customer.total_bookings || 0} bookings
                    </span>
                  </div>
                  <div className={styles.metaItem}>
                    <span className={styles.metaIcon}>📞</span>
                    <span className={styles.metaText}>
                      {customer.phone || 'No phone'}
                    </span>
                  </div>
                </div>

                <div className={styles.customerActions}>
                  <button
                    onClick={() => handleCustomerClick(customer)}
                    className={styles.viewButton}
                  >
                    View Details
                  </button>
                  <Link
                    href={`/admin/bookings/new?customer=${customer.id}`}
                    className={styles.bookButton}
                  >
                    New Booking
                  </Link>
                  <Link
                    href={`/admin/customers/${customer.id}/edit`}
                    className={styles.editButton}
                  >
                    Edit
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.customersTable}>
            <table>
              <thead>
                <tr>
                  <th className={styles.sortableHeader}>Customer ID</th>
                  <th className={styles.sortableHeader}>Name</th>
                  <th className={`${styles.sortableHeader} ${styles.hideOnMobile}`}>Email</th>
                  <th className={`${styles.sortableHeader} ${styles.hideOnMobile}`}>Phone</th>
                  <th className={styles.sortableHeader}>Status</th>
                  <th className={styles.sortableHeader}>Priority</th>
                  <th className={styles.sortableHeader}>Total Bookings</th>
                  <th className={`${styles.sortableHeader} ${styles.hideOnMobile}`}>Last Booking Date</th>
                  <th className={styles.actionsHeader}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentCustomers.map(customer => {
                  const priority = getCustomerPriority(customer);
                  const priorityInfo = getPriorityInfo(priority);
                  const statusInfo = getCustomerStatusInfo(customer);

                  return (
                    <tr key={customer.id}>
                      <td className={styles.customerIdCell}>
                        <span className={styles.customerId}>
                          {getCustomerIdDisplay(customer.id)}
                        </span>
                      </td>
                      <td className={styles.customerNameCell}>
                        <div className={styles.customerNameWithAvatar}>
                          <div className={styles.customerAvatarSmall}>
                            {getCustomerInitials(customer)}
                          </div>
                          <div className={styles.customerNameInfo}>
                            <div className={styles.customerFullName}>
                              {customer.first_name} {customer.last_name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className={`${styles.customerEmailCell} ${styles.hideOnMobile}`}>
                        <span className={styles.customerEmail}>
                          {customer.email || 'No email'}
                        </span>
                      </td>
                      <td className={`${styles.customerPhoneCell} ${styles.hideOnMobile}`}>
                        <span className={styles.customerPhone}>
                          {customer.phone || 'No phone'}
                        </span>
                      </td>
                      <td className={styles.customerStatusCell}>
                        <span className={`${styles.statusBadgeTable} ${
                          statusInfo.status === 'active' ? styles.statusActiveTable : styles.statusNewTable
                        }`}>
                          {statusInfo.label}
                        </span>
                      </td>
                      <td className={styles.customerPriorityCell}>
                        <span
                          className={styles.priorityBadge}
                          style={{ backgroundColor: priorityInfo.color }}
                        >
                          <span className={styles.priorityIcon}>{priorityInfo.icon}</span>
                          <span className={styles.priorityLabel}>{priorityInfo.label}</span>
                        </span>
                      </td>
                      <td className={styles.customerBookingsCell}>
                        <span className={styles.bookingsBadgeTable}>
                          {customer.total_bookings || 0}
                        </span>
                      </td>
                      <td className={`${styles.customerLastBookingCell} ${styles.hideOnMobile}`}>
                        <span className={styles.lastBookingDate}>
                          {getLastBookingDate(customer)}
                        </span>
                      </td>
                      <td className={styles.customerActionsCell}>
                        <div className={styles.tableActions}>
                          <button
                            onClick={() => handleCustomerClick(customer)}
                            className={styles.viewBtnTable}
                            title="View Details"
                          >
                            👁️
                          </button>
                          <Link
                            href={`/admin/bookings/new?customer=${customer.id}`}
                            className={styles.bookBtnTable}
                            title="New Booking"
                          >
                            📅
                          </Link>
                          <Link
                            href={`/admin/customers/${customer.id}/edit`}
                            className={styles.editBtnTable}
                            title="Edit Customer"
                          >
                            ✏️
                          </Link>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={styles.paginationBtn}
            >
              ← Previous
            </button>

            <div className={styles.paginationInfo}>
              Page {currentPage} of {totalPages}
            </div>

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={styles.paginationBtn}
            >
              Next →
            </button>
          </div>
        )}

        {showModal && selectedCustomer && (
          <CustomerModal
            customer={selectedCustomer}
            onClose={() => {
              setShowModal(false);
              setSelectedCustomer(null);
            }}
            onUpdate={handleCustomerUpdate}
            onDelete={handleCustomerDelete}
          />
        )}
      </div>
    </>
  );
}
